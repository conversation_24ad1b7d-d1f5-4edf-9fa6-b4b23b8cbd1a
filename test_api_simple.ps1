# Simple PowerShell script to test TradingView API
Write-Host "🧪 Testing TradingView API Connection" -ForegroundColor Green

$baseUrl = "http://127.0.0.1:8000"

# Test 1: Check if server is running
Write-Host "📡 Testing server connection..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri $baseUrl -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✅ Server is running (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ Server is not running" -ForegroundColor Red
    Write-Host "💡 Please start your TradingView API server first" -ForegroundColor Yellow
    exit 1
}

# Test 2: Test the API endpoint
Write-Host "📊 Testing scrape_pairs endpoint..." -ForegroundColor Yellow

$testPayload = @{
    pairs = @("EGX:ETEL")
    intervals = @(
        @{ value = "1m" }
    )
} | ConvertTo-Json -Depth 3

Write-Host "Sending request..." -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/scrape_pairs" -Method Post -Body $testPayload -ContentType "application/json" -TimeoutSec 30
    Write-Host "✅ API call successful!" -ForegroundColor Green
    Write-Host "🎉 Your TradingView API is working!" -ForegroundColor Green
} catch {
    Write-Host "❌ API call failed: $($_.Exception.Message)" -ForegroundColor Red
}
