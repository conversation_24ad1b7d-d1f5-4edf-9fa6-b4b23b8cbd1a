from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

class IndicatorDTO(BaseModel):
    pair: str
    interval: str
    register_time: str
    name: str
    value: Optional[float]
    action: str

class PivotDTO(BaseModel):
    pair: str
    interval: str
    register_time: str
    pivot: str
    classic: Optional[float]
    fibo: Optional[float]
    camarilla: Optional[float]
    woodie: Optional[float]
    dm: Optional[float]

class financialDTO(BaseModel):
    pair: str
    price: float
    oscillators: List[IndicatorDTO]
    moving_averages: List[IndicatorDTO]
    pivots: List[PivotDTO]

class Interval(BaseModel):
    value: str

class PairRequest(BaseModel):
    pairs: List[str]
    intervals: List[Interval]
