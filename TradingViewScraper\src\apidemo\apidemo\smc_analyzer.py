"""
Smart Money Concept (SMC) Analysis Engine
Integrates with TradingView data to detect SMC patterns
"""
import uuid
from datetime import datetime
from typing import List, Optional, Dict
from .smc_models import (
    CandleData, SmcPattern, SmcPatternType, SmcDirection, 
    MarketStructure, LiquidityLevel, LiquidityType,
    SmcTradingSignal, SignalType, SmcAnalysisResult
)


class SmcAnalyzer:
    """Smart Money Concept Pattern Detection Engine"""
    
    def __init__(self):
        self.min_gap_percentage = 0.1  # 0.1% minimum gap for FVG
        self.min_volume_ratio = 1.5    # 1.5x average volume for confirmation
        self.swing_threshold = 0.5     # 0.5% minimum swing
        self.min_pattern_confidence = 0.7
        self.min_risk_reward = 2.0
    
    def create_candles_from_tradingview_data(self, financial_data, symbol: str, timeframe: str) -> List[CandleData]:
        """Convert TradingView financial data to candle format for SMC analysis"""
        # Since TradingView scraper returns current price and indicators,
        # we'll simulate historical candles for demonstration
        # In a real implementation, you'd need historical OHLCV data
        
        candles = []
        base_price = financial_data.price
        
        # Generate mock historical candles for SMC analysis
        # In production, you'd fetch real historical data
        for i in range(50):  # Generate 50 candles for analysis
            # Simulate price movement
            price_change = (i * 0.1) * 0.01 * base_price  # Small price movements
            open_price = base_price + price_change
            high_price = open_price + (0.005 * open_price)  # 0.5% higher
            low_price = open_price - (0.005 * open_price)   # 0.5% lower
            close_price = open_price + ((i % 3 - 1) * 0.002 * open_price)  # Random close
            
            candle = CandleData(
                timestamp=datetime.now(),
                open=open_price,
                high=max(open_price, high_price, close_price),
                low=min(open_price, low_price, close_price),
                close=close_price,
                volume=1000.0 + (i * 10)  # Mock volume
            )
            candles.append(candle)
        
        return candles
    
    def detect_fair_value_gaps(self, candles: List[CandleData], symbol: str, timeframe: str) -> List[SmcPattern]:
        """Detect Fair Value Gap patterns"""
        fvg_patterns = []
        
        if len(candles) < 3:
            return fvg_patterns
        
        for i in range(1, len(candles) - 1):
            prev_candle = candles[i - 1]
            current_candle = candles[i]
            next_candle = candles[i + 1]
            
            # Bullish FVG: prev_candle.high < next_candle.low
            if prev_candle.high < next_candle.low:
                gap_size = next_candle.low - prev_candle.high
                gap_percentage = (gap_size / prev_candle.high) * 100
                
                if gap_percentage >= self.min_gap_percentage:
                    pattern = SmcPattern(
                        id=str(uuid.uuid4()),
                        pattern_type=SmcPatternType.FAIR_VALUE_GAP,
                        direction=SmcDirection.BULLISH,
                        symbol=symbol,
                        timeframe=timeframe,
                        timestamp=current_candle.timestamp,
                        start_price=prev_candle.high,
                        end_price=next_candle.low,
                        high=next_candle.low,
                        low=prev_candle.high,
                        confidence=min(0.8 + (gap_percentage / 10), 1.0),
                        volume=current_candle.volume,
                        candles_involved=[i-1, i, i+1]
                    )
                    fvg_patterns.append(pattern)
            
            # Bearish FVG: prev_candle.low > next_candle.high
            elif prev_candle.low > next_candle.high:
                gap_size = prev_candle.low - next_candle.high
                gap_percentage = (gap_size / prev_candle.low) * 100
                
                if gap_percentage >= self.min_gap_percentage:
                    pattern = SmcPattern(
                        id=str(uuid.uuid4()),
                        pattern_type=SmcPatternType.FAIR_VALUE_GAP,
                        direction=SmcDirection.BEARISH,
                        symbol=symbol,
                        timeframe=timeframe,
                        timestamp=current_candle.timestamp,
                        start_price=prev_candle.low,
                        end_price=next_candle.high,
                        high=prev_candle.low,
                        low=next_candle.high,
                        confidence=min(0.8 + (gap_percentage / 10), 1.0),
                        volume=current_candle.volume,
                        candles_involved=[i-1, i, i+1]
                    )
                    fvg_patterns.append(pattern)
        
        return fvg_patterns
    
    def detect_order_blocks(self, candles: List[CandleData], symbol: str, timeframe: str) -> List[SmcPattern]:
        """Detect Order Block patterns"""
        order_blocks = []
        
        if len(candles) < 5:
            return order_blocks
        
        # Calculate average volume
        avg_volume = sum(c.volume for c in candles) / len(candles)
        
        for i in range(2, len(candles) - 2):
            current = candles[i]
            
            # Bullish Order Block: Strong bullish candle with high volume
            is_bullish = current.close > current.open
            body_size = (current.close - current.open) / current.open
            volume_confirmed = current.volume >= avg_volume * self.min_volume_ratio
            
            if is_bullish and body_size >= 0.01 and volume_confirmed:  # 1% body size
                pattern = SmcPattern(
                    id=str(uuid.uuid4()),
                    pattern_type=SmcPatternType.ORDER_BLOCK,
                    direction=SmcDirection.BULLISH,
                    symbol=symbol,
                    timeframe=timeframe,
                    timestamp=current.timestamp,
                    start_price=current.open,
                    end_price=current.close,
                    high=current.high,
                    low=current.low,
                    confidence=min(0.7 + (body_size * 10), 1.0),
                    volume=current.volume,
                    candles_involved=[i]
                )
                order_blocks.append(pattern)
            
            # Bearish Order Block: Strong bearish candle with high volume
            elif not is_bullish:
                body_size = (current.open - current.close) / current.open
                if body_size >= 0.01 and volume_confirmed:
                    pattern = SmcPattern(
                        id=str(uuid.uuid4()),
                        pattern_type=SmcPatternType.ORDER_BLOCK,
                        direction=SmcDirection.BEARISH,
                        symbol=symbol,
                        timeframe=timeframe,
                        timestamp=current.timestamp,
                        start_price=current.open,
                        end_price=current.close,
                        high=current.high,
                        low=current.low,
                        confidence=min(0.7 + (body_size * 10), 1.0),
                        volume=current.volume,
                        candles_involved=[i]
                    )
                    order_blocks.append(pattern)
        
        return order_blocks
    
    def analyze_market_structure(self, candles: List[CandleData]) -> MarketStructure:
        """Analyze market structure for trend and swing points"""
        if len(candles) < 10:
            return MarketStructure(
                trend=SmcDirection.BULLISH,
                strength=0.0,
                higher_highs=[],
                higher_lows=[],
                lower_highs=[],
                lower_lows=[]
            )
        
        # Identify swing points
        swing_highs = []
        swing_lows = []
        
        for i in range(2, len(candles) - 2):
            current = candles[i]
            
            # Check for swing high
            if (current.high > candles[i-1].high and current.high > candles[i-2].high and
                current.high > candles[i+1].high and current.high > candles[i+2].high):
                swing_highs.append(current.high)
            
            # Check for swing low
            if (current.low < candles[i-1].low and current.low < candles[i-2].low and
                current.low < candles[i+1].low and current.low < candles[i+2].low):
                swing_lows.append(current.low)
        
        # Analyze trend structure
        higher_highs = []
        higher_lows = []
        lower_highs = []
        lower_lows = []
        
        # Compare consecutive swing points
        for i in range(1, len(swing_highs)):
            if swing_highs[i] > swing_highs[i-1]:
                higher_highs.append(swing_highs[i])
            else:
                lower_highs.append(swing_highs[i])
        
        for i in range(1, len(swing_lows)):
            if swing_lows[i] > swing_lows[i-1]:
                higher_lows.append(swing_lows[i])
            else:
                lower_lows.append(swing_lows[i])
        
        # Determine overall trend
        bullish_signals = len(higher_highs) + len(higher_lows)
        bearish_signals = len(lower_highs) + len(lower_lows)
        
        trend = SmcDirection.BULLISH if bullish_signals >= bearish_signals else SmcDirection.BEARISH
        total_signals = bullish_signals + bearish_signals
        strength = abs(bullish_signals - bearish_signals) / max(total_signals, 1)
        
        return MarketStructure(
            trend=trend,
            strength=strength,
            higher_highs=higher_highs,
            higher_lows=higher_lows,
            lower_highs=lower_highs,
            lower_lows=lower_lows
        )
    
    def identify_liquidity_levels(self, candles: List[CandleData]) -> List[LiquidityLevel]:
        """Identify key liquidity levels"""
        liquidity_levels = []
        
        if len(candles) < 5:
            return liquidity_levels
        
        # Find significant highs and lows
        for i in range(2, len(candles) - 2):
            current = candles[i]
            
            # Check for resistance level (swing high)
            if (current.high > candles[i-1].high and current.high > candles[i-2].high and
                current.high > candles[i+1].high and current.high > candles[i+2].high):
                
                # Calculate strength based on how many times price tested this level
                strength = self.calculate_level_strength(candles, current.high)
                
                liquidity_levels.append(LiquidityLevel(
                    price=current.high,
                    strength=strength,
                    level_type=LiquidityType.RESISTANCE,
                    timestamp=current.timestamp
                ))
            
            # Check for support level (swing low)
            if (current.low < candles[i-1].low and current.low < candles[i-2].low and
                current.low < candles[i+1].low and current.low < candles[i+2].low):
                
                strength = self.calculate_level_strength(candles, current.low)
                
                liquidity_levels.append(LiquidityLevel(
                    price=current.low,
                    strength=strength,
                    level_type=LiquidityType.SUPPORT,
                    timestamp=current.timestamp
                ))
        
        return liquidity_levels
    
    def calculate_level_strength(self, candles: List[CandleData], level_price: float) -> float:
        """Calculate the strength of a liquidity level based on touches"""
        tolerance = level_price * 0.001  # 0.1% tolerance
        touches = 0
        
        for candle in candles:
            if (abs(candle.high - level_price) <= tolerance or 
                abs(candle.low - level_price) <= tolerance):
                touches += 1
        
        return min(touches / 5.0, 1.0)  # Normalize to 0-1
    
    def generate_trading_signals(self, patterns: List[SmcPattern], market_structure: MarketStructure) -> List[SmcTradingSignal]:
        """Generate trading signals from SMC patterns"""
        signals = []
        
        for pattern in patterns:
            if pattern.confidence < self.min_pattern_confidence:
                continue
            
            # Generate signal based on pattern type
            if pattern.pattern_type == SmcPatternType.FAIR_VALUE_GAP:
                signal = self.create_fvg_signal(pattern, market_structure)
                if signal:
                    signals.append(signal)
            
            elif pattern.pattern_type == SmcPatternType.ORDER_BLOCK:
                signal = self.create_order_block_signal(pattern, market_structure)
                if signal:
                    signals.append(signal)
        
        return signals
    
    def create_fvg_signal(self, pattern: SmcPattern, market_structure: MarketStructure) -> Optional[SmcTradingSignal]:
        """Create trading signal from FVG pattern"""
        entry_price = pattern.low if pattern.direction == SmcDirection.BULLISH else pattern.high
        gap_size = abs(pattern.high - pattern.low)
        
        if pattern.direction == SmcDirection.BULLISH:
            stop_loss = pattern.low - (gap_size * 0.5)
            take_profit = pattern.high + (gap_size * 2.0)
        else:
            stop_loss = pattern.high + (gap_size * 0.5)
            take_profit = pattern.low - (gap_size * 2.0)
        
        risk = abs(entry_price - stop_loss)
        reward = abs(take_profit - entry_price)
        risk_reward_ratio = reward / risk if risk > 0 else 0
        
        if risk_reward_ratio < self.min_risk_reward:
            return None
        
        # Check alignment with market structure
        structure_alignment = 1.0 if pattern.direction == market_structure.trend else 0.5
        strength = (pattern.confidence + structure_alignment) / 2.0
        
        return SmcTradingSignal(
            signal_type=SignalType.FVG_ENTRY,
            direction=pattern.direction,
            strength=strength,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            risk_reward_ratio=risk_reward_ratio,
            patterns_involved=[pattern.id],
            confidence=pattern.confidence
        )
    
    def create_order_block_signal(self, pattern: SmcPattern, market_structure: MarketStructure) -> Optional[SmcTradingSignal]:
        """Create trading signal from Order Block pattern"""
        entry_price = pattern.low if pattern.direction == SmcDirection.BULLISH else pattern.high
        block_size = abs(pattern.high - pattern.low)
        
        if pattern.direction == SmcDirection.BULLISH:
            stop_loss = pattern.low - (block_size * 0.3)
            take_profit = pattern.high + (block_size * 3.0)
        else:
            stop_loss = pattern.high + (block_size * 0.3)
            take_profit = pattern.low - (block_size * 3.0)
        
        risk = abs(entry_price - stop_loss)
        reward = abs(take_profit - entry_price)
        risk_reward_ratio = reward / risk if risk > 0 else 0
        
        if risk_reward_ratio < self.min_risk_reward:
            return None
        
        structure_alignment = 1.0 if pattern.direction == market_structure.trend else 0.5
        strength = (pattern.confidence + structure_alignment) / 2.0
        
        return SmcTradingSignal(
            signal_type=SignalType.ORDER_BLOCK_ENTRY,
            direction=pattern.direction,
            strength=strength,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            risk_reward_ratio=risk_reward_ratio,
            patterns_involved=[pattern.id],
            confidence=pattern.confidence
        )
    
    def analyze_symbol(self, financial_data, symbol: str, timeframe: str) -> SmcAnalysisResult:
        """Complete SMC analysis for a symbol and timeframe"""
        # Convert TradingView data to candles
        candles = self.create_candles_from_tradingview_data(financial_data, symbol, timeframe)
        
        # Detect patterns
        fvg_patterns = self.detect_fair_value_gaps(candles, symbol, timeframe)
        order_block_patterns = self.detect_order_blocks(candles, symbol, timeframe)
        
        # Combine all patterns
        all_patterns = fvg_patterns + order_block_patterns
        
        # Analyze market structure
        market_structure = self.analyze_market_structure(candles)
        
        # Identify liquidity levels
        liquidity_levels = self.identify_liquidity_levels(candles)
        
        # Generate trading signals
        trading_signals = self.generate_trading_signals(all_patterns, market_structure)
        
        return SmcAnalysisResult(
            symbol=symbol,
            timeframe=timeframe,
            timestamp=datetime.now(),
            patterns=all_patterns,
            market_structure=market_structure,
            liquidity_levels=liquidity_levels,
            trading_signals=trading_signals
        )
