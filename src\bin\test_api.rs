use paragon::{
    data_sources::{DataSource, TradingViewSource},
    config::Config,
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 Testing TradingView API Integration");
    
    // Load configuration
    let config = Config::from_env();
    config.validate()?;
    
    println!("📡 API URL: {}", config.tradingview.api_base_url);
    
    // Initialize TradingView source
    let tv_source = TradingViewSource::new();
    
    // Test symbols
    let test_symbols = vec![
        "EGX:ETEL".to_string(),
        "EGX:CIB".to_string(),
        "FOREX:EURUSD".to_string(),
    ];
    
    println!("🔍 Testing symbols: {:?}", test_symbols);
    
    for symbol in test_symbols {
        println!("\n📊 Testing symbol: {}", symbol);
        
        // Test symbol support
        if tv_source.supports_symbol(&symbol) {
            println!("✅ Symbol {} is supported", symbol);
        } else {
            println!("❌ Symbol {} is not supported", symbol);
            continue;
        }
        
        // Test current price fetch
        match tv_source.fetch_current_price(&symbol).await {
            Ok(price) => println!("💰 Current price: ${:.4}", price),
            Err(e) => println!("❌ Failed to fetch price: {}", e),
        }
        
        // Test historical candles fetch
        match tv_source.fetch_historical_candles(&symbol, "1m", Some(5)).await {
            Ok(candles) => {
                println!("📈 Fetched {} candles", candles.len());
                for (i, candle) in candles.iter().enumerate() {
                    println!("  Candle {}: {} @ {} (O:{:.4} H:{:.4} L:{:.4} C:{:.4})", 
                        i + 1, 
                        candle.symbol, 
                        candle.timestamp.format("%Y-%m-%d %H:%M:%S"),
                        candle.open,
                        candle.high,
                        candle.low,
                        candle.close
                    );
                }
            },
            Err(e) => println!("❌ Failed to fetch candles: {}", e),
        }
        
        // Add delay between requests
        tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
    }
    
    println!("\n✅ API integration test completed!");
    Ok(())
}
