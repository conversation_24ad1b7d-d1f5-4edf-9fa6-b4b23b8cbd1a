use paragon::data_sources::{DataSource, BinanceDataSource};
use paragon::smc::{SmcE<PERSON><PERSON>, SmcStrategyAnalyzer, SmcStrategyConfig};
use paragon::config::Config;
use std::env;
use std::collections::HashMap;
use tokio;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧠 Starting Paragon - Smart Money Concept (SMC) Trading Platform");
    println!("{}", "=".repeat(60));
    
    // Load configuration
    let config = Config::from_file("config.toml").unwrap_or_default();
    println!("📊 Configuration loaded");

    // Initialize SMC Engine
    let smc_engine = SmcEngine::new();
    let strategy_config = SmcStrategyConfig::default();
    let strategy_analyzer = SmcStrategyAnalyzer::new(strategy_config);
    println!("🧠 SMC Engine initialized");

    // Initialize data source based on environment variable
    let data_source_type = env::var("DATA_SOURCE").unwrap_or_else(|_| "binance".to_string());
    
    match data_source_type.as_str() {
        "binance" => {
            println!("🔗 Using Binance data source");
            let binance = BinanceDataSource::new();
            run_smc_analysis_with_source(binance, &smc_engine, &strategy_analyzer).await?;
        },
        "tradingview" => {
            println!("📺 TradingView data source not implemented yet");
            println!("💡 Use DATA_SOURCE=binance for now");
            return Ok(());
        },
        _ => {
            println!("❌ Unknown data source: {}", data_source_type);
            println!("Available sources: binance, tradingview");
            return Ok(());
        }
    }

    Ok(())
}

async fn run_smc_analysis_with_source<T: DataSource>(
    data_source: T,
    smc_engine: &SmcEngine,
    strategy_analyzer: &SmcStrategyAnalyzer,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🧠 Starting Smart Money Concept Analysis...");
    println!("{}", "=".repeat(50));
    
    // SMC-focused symbols (major pairs with good liquidity)
    let symbols = vec!["BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT"];
    let timeframes = vec!["1h", "4h", "1d"];
    
    for symbol in symbols {
        println!("\n🔍 SMC Analysis for {}", symbol);
        println!("{}", "-".repeat(30));
        
        let mut timeframe_data = HashMap::new();
        
        // Collect data for all timeframes
        for timeframe in &timeframes {
            if data_source.supports_symbol(symbol) {
                match data_source.fetch_historical_candles(symbol, timeframe, Some(200)).await {
                    Ok(candles) => {
                        println!("  📊 {} {}: {} candles fetched", symbol, timeframe, candles.len());
                        timeframe_data.insert(timeframe.to_string(), candles);
                    },
                    Err(e) => {
                        println!("  ❌ {} {}: Error - {}", symbol, timeframe, e);
                    }
                }
            }
        }
        
        // Perform SMC analysis for each timeframe
        for (timeframe, candles) in &timeframe_data {
            if candles.len() >= 50 { // Need enough data for SMC analysis
                println!("\n  🧠 SMC Analysis - {} {}", symbol, timeframe);
                
                // Run SMC pattern detection
                let smc_result = smc_engine.analyze_candles(candles);
                
                println!("    📈 Market Structure: {:?} (Strength: {:.2})", 
                    smc_result.market_structure.trend, 
                    smc_result.market_structure.strength);
                
                // Display detected patterns
                let fvg_count = smc_result.patterns.iter()
                    .filter(|p| matches!(p.pattern_type, paragon::smc::SmcPatternType::FairValueGap))
                    .count();
                let ob_count = smc_result.patterns.iter()
                    .filter(|p| matches!(p.pattern_type, paragon::smc::SmcPatternType::OrderBlock))
                    .count();
                let choch_count = smc_result.patterns.iter()
                    .filter(|p| matches!(p.pattern_type, paragon::smc::SmcPatternType::ChangeOfCharacter))
                    .count();
                
                println!("    🎯 Patterns Found:");
                println!("      • Fair Value Gaps: {}", fvg_count);
                println!("      • Order Blocks: {}", ob_count);
                println!("      • CHoCH: {}", choch_count);
                println!("      • Liquidity Levels: {}", smc_result.liquidity_levels.len());
                
                // Display high-confidence patterns
                let high_confidence_patterns: Vec<_> = smc_result.patterns.iter()
                    .filter(|p| p.confidence >= 0.7)
                    .collect();
                
                if !high_confidence_patterns.is_empty() {
                    println!("    ⭐ High Confidence Patterns:");
                    for pattern in high_confidence_patterns.iter().take(3) {
                        println!("      • {:?} {:?} (Confidence: {:.2})", 
                            pattern.pattern_type, pattern.direction, pattern.confidence);
                    }
                }
            }
        }
        
        // Generate trading signals using multi-timeframe analysis
        if !timeframe_data.is_empty() {
            println!("\n  🎯 Generating Trading Signals...");
            let signals = strategy_analyzer.multi_timeframe_analysis(timeframe_data);
            
            if signals.is_empty() {
                println!("    📭 No trading signals generated");
            } else {
                println!("    🚨 {} Trading Signals Generated:", signals.len());
                
                for (i, signal) in signals.iter().take(3).enumerate() {
                    println!("    {}. {:?} {:?}", i + 1, signal.signal_type, signal.direction);
                    println!("       Entry: {:.4} | SL: {:.4} | TP: {:.4}", 
                        signal.entry_price, signal.stop_loss, signal.take_profit);
                    println!("       R:R = 1:{:.2} | Confidence: {:.2}", 
                        signal.risk_reward_ratio, signal.confidence);
                }
                
                if signals.len() > 3 {
                    println!("    ... and {} more signals", signals.len() - 3);
                }
            }
        }
    }
    
    println!("\n🎉 SMC Analysis Complete!");
    println!("💡 This is the foundation for Smart Money Concept trading strategies");
    Ok(())
}
