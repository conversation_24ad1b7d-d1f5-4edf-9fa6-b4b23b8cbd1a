use crate::{Candle, data_sources::traits::{DataSource, DataSourceError}};
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use reqwest::Client;

/// TradingView data source using API calls to your scraper service
pub struct TradingViewSource {
    api_base_url: String,
    client: Client,
    max_concurrent_requests: usize,
}

/// Request structure for the TradingView API
#[derive(Serialize)]
struct ApiRequest {
    pairs: Vec<String>,
    intervals: Vec<ApiInterval>,
}

#[derive(Serialize)]
struct ApiInterval {
    value: String,
}

/// Response structure from your TradingView API
#[derive(Deserialize)]
struct ApiResponse {
    // Your API returns {"result": {...}}
    result: std::collections::HashMap<String, Vec<FinancialData>>,
}

#[derive(Deserialize)]
struct FinancialData {
    pair: String,
    price: f64,
    oscillators: Vec<IndicatorData>,
    moving_averages: Vec<IndicatorData>,
    pivots: Vec<PivotData>,
}

#[derive(Deserialize)]
struct IndicatorData {
    pair: String,
    interval: String,
    register_time: String,
    name: String,
    value: Option<f64>,
    action: String,
}

#[derive(Deserialize)]
struct PivotData {
    pair: String,
    interval: String,
    register_time: String,
    pivot: String,
    classic: Option<f64>,
    fibo: Option<f64>,
    camarilla: Option<f64>,
    woodie: Option<f64>,
    dm: Option<f64>,
}

/// Simplified candle data from TradingView
#[derive(Deserialize)]
struct TradingViewCandle {
    timestamp: i64,
    open: f64,
    high: f64,
    low: f64,
    close: f64,
    volume: f64,
}

impl TradingViewSource {
    pub fn new(api_base_url: String) -> Self {
        Self {
            api_base_url,
            client: Client::new(),
            max_concurrent_requests: 5,
        }
    }

    pub fn with_config(api_base_url: String, max_concurrent: usize) -> Self {
        Self {
            api_base_url,
            client: Client::new(),
            max_concurrent_requests: max_concurrent,
        }
    }

    /// Call the TradingView API and get results
    async fn call_api(
        &self,
        symbols: &[String],
        intervals: &[String],
    ) -> Result<ApiResponse, DataSourceError> {
        let request = ApiRequest {
            pairs: symbols.to_vec(),
            intervals: intervals.iter().map(|i| ApiInterval { value: i.clone() }).collect(),
        };

        let url = format!("{}/api/scrape_pairs", self.api_base_url);

        let response = self.client
            .post(&url)
            .json(&request)
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(DataSourceError::Parse(format!("API error {}: {}", status, error_text)));
        }

        let api_response: ApiResponse = response.json().await?;
        Ok(api_response)
    }

    /// Convert FinancialData to Candle struct
    fn convert_to_candle(
        &self,
        symbol: &str,
        interval: &str,
        financial_data: &FinancialData,
    ) -> Candle {
        // Since your API returns current price, we'll create a simple candle
        // In a real scenario, you might want to collect multiple prices over time
        let price = financial_data.price;

        Candle {
            symbol: symbol.to_string(),
            timerange: interval.to_string(),
            timestamp: Utc::now(), // Current timestamp
            open: price,
            high: price, // For real-time data, high/low might be the same as current price
            low: price,
            close: price,
            volume: 1000.0, // Default volume - you might want to get this from your API
        }
    }
}

#[async_trait]
impl DataSource for TradingViewSource {
    async fn fetch_historical_candles(
        &self,
        symbol: &str,
        interval: &str,
        _limit: Option<usize>,
    ) -> Result<Vec<Candle>, DataSourceError> {
        let symbols = vec![symbol.to_string()];
        let intervals = vec![interval.to_string()];

        let api_response = self.call_api(&symbols, &intervals).await?;

        // Extract data for the requested symbol
        let mut candles = Vec::new();

        if let Some(financial_data_list) = api_response.result.get(symbol) {
            for financial_data in financial_data_list {
                let candle = self.convert_to_candle(symbol, interval, financial_data);
                candles.push(candle);
            }
        }

        Ok(candles)
    }

    async fn fetch_current_price(&self, symbol: &str) -> Result<f64, DataSourceError> {
        let symbols = vec![symbol.to_string()];
        let intervals = vec!["1m".to_string()]; // Use 1-minute for current price

        let api_response = self.call_api(&symbols, &intervals).await?;

        // Extract price for the requested symbol
        if let Some(financial_data_list) = api_response.result.get(symbol) {
            if let Some(financial_data) = financial_data_list.first() {
                return Ok(financial_data.price);
            }
        }

        Err(DataSourceError::Parse(format!("No price data found for symbol: {}", symbol)))
    }

    fn supports_symbol(&self, symbol: &str) -> bool {
        // TradingView supports most symbols, but you can add specific validation
        // For EGX stocks, check if it starts with "EGX-" or similar
        symbol.contains("EGX-") ||
        symbol.contains("FOREX:") ||
        symbol.contains("BINANCE:") ||
        !symbol.is_empty() // Basic validation
    }

    fn supported_intervals(&self) -> Vec<&'static str> {
        vec!["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"]
    }
}

/// EGX-specific symbols and utilities
pub struct EgxSymbols;

impl EgxSymbols {
    /// Popular EGX stocks (matching your API format)
    pub const POPULAR_STOCKS: &'static [&'static str] = &[
        "EGX-COMI",  // Commercial International Bank
        "EGX-FWRY",  // Fawry Banking Technology
        "EGX-PHDC",  // Palm Hills Development
        "EGX-EFID",  // Edita Food Industries
        "EGX-UBEE",  // United Bank Egypt
        "EGX-GGRN",  // GoGreen Agricultural
        "EGX-OBRI",  // Orascom Business Intelligence
        "EGX-UTOP",  // United Top
    ];

    /// Get all EGX symbols as Vec<String>
    pub fn get_all_symbols() -> Vec<String> {
        Self::POPULAR_STOCKS.iter().map(|s| s.to_string()).collect()
    }

    /// Check if a symbol is an EGX stock
    pub fn is_egx_symbol(symbol: &str) -> bool {
        symbol.starts_with("EGX-")
    }
}
