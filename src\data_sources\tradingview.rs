use crate::{Candle, data_sources::traits::{DataSource, DataSourceError}};
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::process::Command as TokioCommand;

/// TradingView data source using direct scraping
pub struct TradingViewSource {
    scraper_script_path: String,
    max_concurrent_requests: usize,
}

/// Response structure from the Python scraper
#[derive(Deserialize)]
struct ScraperResponse {
    result: HashMap<String, Vec<FinancialData>>,
}

#[derive(Deserialize)]
struct FinancialData {
    pair: String,
    price: f64,
    oscillators: Vec<IndicatorData>,
    moving_averages: Vec<IndicatorData>,
    pivots: Vec<PivotData>,
}

#[derive(Deserialize)]
struct IndicatorData {
    pair: String,
    interval: String,
    register_time: String,
    name: String,
    value: Option<f64>,
    action: String,
}

#[derive(Deserialize)]
struct PivotData {
    pair: String,
    interval: String,
    register_time: String,
    pivot: String,
    classic: Option<f64>,
    fibo: Option<f64>,
    camarilla: Option<f64>,
    woodie: Option<f64>,
    dm: Option<f64>,
}

/// Simplified candle data from TradingView
#[derive(Deserialize)]
struct TradingViewCandle {
    timestamp: i64,
    open: f64,
    high: f64,
    low: f64,
    close: f64,
    volume: f64,
}

impl TradingViewSource {
    pub fn new(scraper_script_path: String) -> Self {
        Self {
            scraper_script_path,
            max_concurrent_requests: 5,
        }
    }

    pub fn with_config(scraper_script_path: String, max_concurrent: usize) -> Self {
        Self {
            scraper_script_path,
            max_concurrent_requests: max_concurrent,
        }
    }

    /// Execute the Python scraper and get results
    async fn call_scraper(
        &self,
        symbols: &[String],
        intervals: &[String],
    ) -> Result<ScraperResponse, DataSourceError> {
        // Serialize arguments for Python script
        let pairs_json = serde_json::to_string(symbols)
            .map_err(|e| DataSourceError::Parse(format!("Failed to serialize pairs: {}", e)))?;
        let intervals_json = serde_json::to_string(intervals)
            .map_err(|e| DataSourceError::Parse(format!("Failed to serialize intervals: {}", e)))?;

        // Execute Python scraper
        let output = TokioCommand::new("python")
            .arg(&self.scraper_script_path)
            .arg(&pairs_json)
            .arg(&intervals_json)
            .output()
            .await
            .map_err(|e| DataSourceError::Parse(format!("Failed to execute scraper: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(DataSourceError::Parse(format!("Scraper failed: {}", stderr)));
        }

        // Parse output
        let stdout = String::from_utf8_lossy(&output.stdout);

        // Extract JSON result between markers
        if let Some(start) = stdout.find("RESULT_START") {
            if let Some(end) = stdout.find("RESULT_END") {
                let json_str = &stdout[start + "RESULT_START".len()..end].trim();
                let response: ScraperResponse = serde_json::from_str(json_str)
                    .map_err(|e| DataSourceError::Parse(format!("Failed to parse scraper output: {}", e)))?;
                return Ok(response);
            }
        }

        Err(DataSourceError::Parse("No valid result found in scraper output".to_string()))
    }

    /// Convert FinancialData to Candle struct
    fn convert_to_candle(
        &self,
        symbol: &str,
        interval: &str,
        financial_data: &FinancialData,
    ) -> Candle {
        // Since your API returns current price, we'll create a simple candle
        // In a real scenario, you might want to collect multiple prices over time
        let price = financial_data.price;

        Candle {
            symbol: symbol.to_string(),
            timerange: interval.to_string(),
            timestamp: Utc::now(), // Current timestamp
            open: price,
            high: price, // For real-time data, high/low might be the same as current price
            low: price,
            close: price,
            volume: 1000.0, // Default volume - you might want to get this from your API
        }
    }
}

#[async_trait]
impl DataSource for TradingViewSource {
    async fn fetch_historical_candles(
        &self,
        symbol: &str,
        interval: &str,
        _limit: Option<usize>,
    ) -> Result<Vec<Candle>, DataSourceError> {
        let symbols = vec![symbol.to_string()];
        let intervals = vec![interval.to_string()];

        let scraper_response = self.call_scraper(&symbols, &intervals).await?;

        // Extract data for the requested symbol
        let mut candles = Vec::new();

        if let Some(financial_data_list) = scraper_response.result.get(symbol) {
            for financial_data in financial_data_list {
                let candle = self.convert_to_candle(symbol, interval, financial_data);
                candles.push(candle);
            }
        }

        Ok(candles)
    }

    async fn fetch_current_price(&self, symbol: &str) -> Result<f64, DataSourceError> {
        let symbols = vec![symbol.to_string()];
        let intervals = vec!["1m".to_string()]; // Use 1-minute for current price

        let scraper_response = self.call_scraper(&symbols, &intervals).await?;

        // Extract price for the requested symbol
        if let Some(financial_data_list) = scraper_response.result.get(symbol) {
            if let Some(financial_data) = financial_data_list.first() {
                return Ok(financial_data.price);
            }
        }

        Err(DataSourceError::Parse(format!("No price data found for symbol: {}", symbol)))
    }

    fn supports_symbol(&self, symbol: &str) -> bool {
        // TradingView supports most symbols, but you can add specific validation
        // For EGX stocks, check if it starts with "EGX-" or similar
        symbol.contains("EGX-") ||
        symbol.contains("FOREX:") ||
        symbol.contains("BINANCE:") ||
        !symbol.is_empty() // Basic validation
    }

    fn supported_intervals(&self) -> Vec<&'static str> {
        vec!["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"]
    }
}

/// EGX-specific symbols and utilities
pub struct EgxSymbols;

impl EgxSymbols {
    /// Popular EGX stocks (matching your API format)
    pub const POPULAR_STOCKS: &'static [&'static str] = &[
        "EGX-COMI",  // Commercial International Bank
        "EGX-FWRY",  // Fawry Banking Technology
        "EGX-PHDC",  // Palm Hills Development
        "EGX-EFID",  // Edita Food Industries
        "EGX-UBEE",  // United Bank Egypt
        "EGX-GGRN",  // GoGreen Agricultural
        "EGX-OBRI",  // Orascom Business Intelligence
        "EGX-UTOP",  // United Top
    ];

    /// Get all EGX symbols as Vec<String>
    pub fn get_all_symbols() -> Vec<String> {
        Self::POPULAR_STOCKS.iter().map(|s| s.to_string()).collect()
    }

    /// Check if a symbol is an EGX stock
    pub fn is_egx_symbol(symbol: &str) -> bool {
        symbol.starts_with("EGX-")
    }
}
