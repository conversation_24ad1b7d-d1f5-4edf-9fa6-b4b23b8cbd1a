"""URL configuration for apidemo project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from django.shortcuts import render
from ninja import NinjaAPI
from .scrape_pairs import main
from .models import PairRequest
from .smc_models import SmcRequest
from .smc_integration import analyze_pairs_with_smc, format_smc_results_for_web

api = NinjaAPI()


def home(request):
    """Serve the main HTML interface"""
    return render(request, 'index.html')


@api.post("/scrape_pairs")
async def scrape_pairs(request, pair_request: PairRequest):
    print(f"Received pair_request: {pair_request}")
    res = await main(pair_request)
    return {"result": res}


@api.post("/smc_analysis")
async def smc_analysis(request, smc_request: SmcRequest):
    """SMC Analysis endpoint - combines TradingView scraping with SMC pattern detection"""
    print(f"🧠 Received SMC analysis request: {smc_request}")

    # Perform SMC analysis
    smc_response = await analyze_pairs_with_smc(smc_request)

    # Format results for web display
    formatted_results = format_smc_results_for_web(smc_response)

    return formatted_results


@api.post("/quick_smc")
async def quick_smc(request, pair_request: PairRequest):
    """Quick SMC analysis using the same input format as scrape_pairs"""
    print(f"🚀 Quick SMC analysis for: {pair_request}")

    # Convert PairRequest to SmcRequest
    smc_request = SmcRequest(
        pairs=pair_request.pairs,
        intervals=[interval.value for interval in pair_request.intervals],
        include_signals=True,
        min_confidence=0.7
    )

    # Perform SMC analysis
    smc_response = await analyze_pairs_with_smc(smc_request)

    # Format results for web display
    formatted_results = format_smc_results_for_web(smc_response)

    return formatted_results


urlpatterns = [
    path("", home, name="home"),
    path("admin/", admin.site.urls),
    path("api/", api.urls),
]
