use paragon::data_sources::{DataSource, TradingViewDataSource, EgxSymbols};
use tokio;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Testing TradingView API Integration with EGX Stocks");
    println!("================================================");

    // Initialize TradingView data source
    let tradingview = TradingViewDataSource::new("http://127.0.0.1:8000".to_string());

    // Test with EGX stock
    println!("Testing EGX stock data...");
    let egx_symbol = "EGX-COMI"; // Commercial International Bank
    match tradingview.get_candles(egx_symbol, "1d", 10).await {
        Ok(candles) => {
            println!("✅ Successfully fetched {} candles for {}", candles.len(), egx_symbol);
            if let Some(latest) = candles.last() {
                println!("   Latest candle: Open: {:.2}, High: {:.2}, Low: {:.2}, Close: {:.2}", 
                    latest.open, latest.high, latest.low, latest.close);
            }
        }
        Err(e) => println!("❌ Error fetching EGX data: {}", e),
    }

    // Test current price
    println!("\nTesting current price...");
    match tradingview.get_current_price(egx_symbol).await {
        Ok(price) => println!("✅ Current price for {}: {:.2} EGP", egx_symbol, price),
        Err(e) => println!("❌ Error fetching current price: {}", e),
    }

    // Test multiple EGX symbols
    println!("\nTesting multiple EGX symbols...");
    let symbols = EgxSymbols::get_all_symbols();
    for symbol in symbols.iter().take(3) { // Test first 3 symbols
        println!("Testing symbol: {}", symbol);
        match tradingview.get_current_price(symbol).await {
            Ok(price) => println!("  ✅ {}: {:.2} EGP", symbol, price),
            Err(e) => println!("  ❌ {}: Error - {}", symbol, e),
        }
    }

    println!("\n🎉 TradingView API integration test completed!");
    Ok(())
}
