#!/usr/bin/env python3
"""
Quick test to verify TradingView scraper integration
"""

import subprocess
import sys
import json

def test_scraper():
    """Test the Python scraper directly"""
    print("🧪 Testing TradingView scraper integration...")
    
    # Test with a single EGX symbol
    pairs = ["EGX-COMI"]
    intervals = ["1d"]
    
    command = [
        sys.executable,
        "src/data_sources/tradingview_scraper.py",
        json.dumps(pairs),
        json.dumps(intervals)
    ]
    
    print(f"🔄 Running: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            output = result.stdout
            if "RESULT_START" in output and "RESULT_END" in output:
                # Extract JSON result
                start = output.find("RESULT_START") + len("RESULT_START")
                end = output.find("RESULT_END")
                json_str = output[start:end].strip()
                
                try:
                    data = json.loads(json_str)
                    print("✅ Scraper test successful!")
                    print(f"📊 Data structure: {list(data.keys())}")
                    
                    if "result" in data and "EGX-COMI" in data["result"]:
                        comi_data = data["result"]["EGX-COMI"]
                        if comi_data:
                            first_item = comi_data[0]
                            print(f"💰 EGX-COMI price: {first_item.get('price', 'N/A')} EGP")
                            print(f"📈 Oscillators: {len(first_item.get('oscillators', []))}")
                            print(f"📊 Moving Averages: {len(first_item.get('moving_averages', []))}")
                            print(f"🎯 Pivots: {len(first_item.get('pivots', []))}")
                    
                    return True
                except json.JSONDecodeError as e:
                    print(f"❌ Failed to parse JSON result: {e}")
                    print(f"Raw output: {json_str[:200]}...")
                    return False
            else:
                print("❌ No valid result markers found in output")
                print(f"stdout: {result.stdout}")
                print(f"stderr: {result.stderr}")
                return False
        else:
            print(f"❌ Scraper failed with return code: {result.returncode}")
            print(f"stderr: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Scraper test timed out (120 seconds)")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    print("🚀 Paragon SMC - TradingView Integration Test")
    print("=" * 50)
    
    success = test_scraper()
    
    if success:
        print("\n🎉 Integration test PASSED!")
        print("✅ Your TradingView scraper is ready for the SMC platform")
        print("\n🎯 Next steps:")
        print("   1. Run: python setup_scraper.py (if not done)")
        print("   2. Run: cargo run --bin main_with_tradingview")
    else:
        print("\n💥 Integration test FAILED!")
        print("❌ Please check the errors above and run setup_scraper.py")

if __name__ == "__main__":
    main()
