"""
SMC Integration with TradingView Scraper
Combines TradingView data scraping with Smart Money Concept analysis
"""
import asyncio
from datetime import datetime
from typing import Dict, List
from .models import PairRequest, financialDTO
from .smc_models import SmcRequest, SmcResponse, SmcAnalysisResult
from .smc_analyzer import <PERSON>mcAnal<PERSON><PERSON>
from .scrape_pairs import main as scrape_tradingview_data


class SmcTradingViewIntegration:
    """Integration class that combines TradingView scraping with SMC analysis"""
    
    def __init__(self):
        self.smc_analyzer = SmcAnalyzer()
    
    async def analyze_with_smc(self, smc_request: SmcRequest) -> SmcResponse:
        """
        Main function that:
        1. Scrapes TradingView data for the requested pairs
        2. Performs SMC analysis on the data
        3. Returns combined results
        """
        try:
            print(f"🧠 Starting SMC analysis for pairs: {smc_request.pairs}")
            print(f"📊 Intervals: {smc_request.intervals}")
            
            # Step 1: Scrape TradingView data
            print("📺 Scraping TradingView data...")
            tradingview_request = PairRequest(
                pairs=smc_request.pairs,
                intervals=[interval for interval in smc_request.intervals]
            )
            
            tradingview_data = await scrape_tradingview_data(tradingview_request)
            print(f"✅ TradingView data scraped for {len(tradingview_data)} pairs")
            
            # Step 2: Perform SMC analysis on each pair and timeframe
            print("🧠 Performing SMC analysis...")
            smc_results: Dict[str, List[SmcAnalysisResult]] = {}
            
            for pair, financial_data_list in tradingview_data.items():
                print(f"  🔍 Analyzing {pair}...")
                smc_results[pair] = []
                
                # Analyze each timeframe for this pair
                for financial_data in financial_data_list:
                    # Extract timeframe from the financial data
                    timeframe = self.extract_timeframe_from_financial_data(financial_data)
                    
                    print(f"    📈 SMC analysis for {pair} {timeframe}")
                    
                    # Perform SMC analysis
                    smc_result = self.smc_analyzer.analyze_symbol(
                        financial_data, pair, timeframe
                    )
                    
                    # Filter patterns by confidence if requested
                    if smc_request.min_confidence > 0:
                        smc_result.patterns = [
                            p for p in smc_result.patterns 
                            if p.confidence >= smc_request.min_confidence
                        ]
                        smc_result.trading_signals = [
                            s for s in smc_result.trading_signals 
                            if s.confidence >= smc_request.min_confidence
                        ]
                    
                    smc_results[pair].append(smc_result)
                    
                    print(f"      🎯 Found {len(smc_result.patterns)} patterns")
                    print(f"      🚨 Generated {len(smc_result.trading_signals)} signals")
            
            print("✅ SMC analysis completed!")
            
            return SmcResponse(
                success=True,
                message=f"SMC analysis completed for {len(smc_results)} pairs",
                timestamp=datetime.now(),
                results=smc_results
            )
            
        except Exception as e:
            print(f"❌ Error in SMC analysis: {str(e)}")
            return SmcResponse(
                success=False,
                message=f"Error: {str(e)}",
                timestamp=datetime.now(),
                results={}
            )
    
    def extract_timeframe_from_financial_data(self, financial_data: financialDTO) -> str:
        """Extract timeframe from financial data"""
        # Try to get timeframe from oscillators or moving averages
        if financial_data.oscillators and len(financial_data.oscillators) > 0:
            return financial_data.oscillators[0].interval
        elif financial_data.moving_averages and len(financial_data.moving_averages) > 0:
            return financial_data.moving_averages[0].interval
        else:
            return "1D"  # Default timeframe
    
    def create_summary_statistics(self, smc_results: Dict[str, List[SmcAnalysisResult]]) -> Dict:
        """Create summary statistics for the SMC analysis"""
        total_patterns = 0
        total_signals = 0
        pattern_types = {}
        signal_types = {}
        
        for pair, results in smc_results.items():
            for result in results:
                total_patterns += len(result.patterns)
                total_signals += len(result.trading_signals)
                
                # Count pattern types
                for pattern in result.patterns:
                    pattern_type = pattern.pattern_type.value
                    pattern_types[pattern_type] = pattern_types.get(pattern_type, 0) + 1
                
                # Count signal types
                for signal in result.trading_signals:
                    signal_type = signal.signal_type.value
                    signal_types[signal_type] = signal_types.get(signal_type, 0) + 1
        
        return {
            "total_pairs_analyzed": len(smc_results),
            "total_patterns_found": total_patterns,
            "total_signals_generated": total_signals,
            "pattern_breakdown": pattern_types,
            "signal_breakdown": signal_types
        }


# Main integration function for the API endpoint
async def analyze_pairs_with_smc(smc_request: SmcRequest) -> SmcResponse:
    """
    Main function to be called from the Django API endpoint
    """
    integration = SmcTradingViewIntegration()
    return await integration.analyze_with_smc(smc_request)


# Convenience function for quick analysis
async def quick_smc_analysis(pairs: List[str], intervals: List[str] = None) -> SmcResponse:
    """
    Quick SMC analysis with default parameters
    """
    if intervals is None:
        intervals = ["1h", "4h", "1D"]
    
    smc_request = SmcRequest(
        pairs=pairs,
        intervals=intervals,
        include_signals=True,
        min_confidence=0.7
    )
    
    return await analyze_pairs_with_smc(smc_request)


# Helper function to format SMC results for web display
def format_smc_results_for_web(smc_response: SmcResponse) -> Dict:
    """
    Format SMC results for web display
    """
    if not smc_response.success:
        return {
            "success": False,
            "error": smc_response.message
        }
    
    formatted_results = {}
    
    for pair, results in smc_response.results.items():
        formatted_results[pair] = {
            "timeframes": {},
            "summary": {
                "total_patterns": 0,
                "total_signals": 0,
                "strongest_signal": None
            }
        }
        
        strongest_signal = None
        strongest_strength = 0
        
        for result in results:
            timeframe_data = {
                "market_structure": {
                    "trend": result.market_structure.trend.value,
                    "strength": round(result.market_structure.strength, 2)
                },
                "patterns": [],
                "signals": [],
                "liquidity_levels": len(result.liquidity_levels)
            }
            
            # Format patterns
            for pattern in result.patterns:
                timeframe_data["patterns"].append({
                    "type": pattern.pattern_type.value,
                    "direction": pattern.direction.value,
                    "confidence": round(pattern.confidence, 2),
                    "price_range": f"{pattern.low:.4f} - {pattern.high:.4f}"
                })
            
            # Format signals
            for signal in result.trading_signals:
                signal_data = {
                    "type": signal.signal_type.value,
                    "direction": signal.direction.value,
                    "strength": round(signal.strength, 2),
                    "entry": round(signal.entry_price, 4),
                    "stop_loss": round(signal.stop_loss, 4),
                    "take_profit": round(signal.take_profit, 4),
                    "risk_reward": round(signal.risk_reward_ratio, 2),
                    "confidence": round(signal.confidence, 2)
                }
                timeframe_data["signals"].append(signal_data)
                
                # Track strongest signal
                if signal.strength > strongest_strength:
                    strongest_strength = signal.strength
                    strongest_signal = signal_data
            
            formatted_results[pair]["timeframes"][result.timeframe] = timeframe_data
            formatted_results[pair]["summary"]["total_patterns"] += len(result.patterns)
            formatted_results[pair]["summary"]["total_signals"] += len(result.trading_signals)
        
        formatted_results[pair]["summary"]["strongest_signal"] = strongest_signal
    
    return {
        "success": True,
        "timestamp": smc_response.timestamp.isoformat(),
        "results": formatted_results
    }
