#!/usr/bin/env python3
"""
Quick test to verify TradingView API is working
"""
import requests
import json

def test_api():
    """Test the TradingView API with a simple request"""
    url = "http://127.0.0.1:8000/api/scrape_pairs"
    
    # Test data - single EGX stock
    test_data = {
        "pairs": ["EGX-COMI"],
        "intervals": ["1D"]
    }
    
    print("🚀 Testing TradingView API...")
    print(f"URL: {url}")
    print(f"Request: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API Response received!")
            print(f"Response keys: {list(data.keys())}")
            
            if 'result' in data:
                result = data['result']
                print(f"Result keys: {list(result.keys())}")
                
                if 'EGX-COMI' in result:
                    comi_data = result['EGX-COMI']
                    print(f"EGX-COMI data length: {len(comi_data)}")
                    if comi_data:
                        first_item = comi_data[0]
                        print(f"First item keys: {list(first_item.keys())}")
                        if 'price' in first_item:
                            print(f"Current price: {first_item['price']} EGP")
                        
            return True
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

if __name__ == "__main__":
    success = test_api()
    if success:
        print("\n🎉 API is working correctly!")
    else:
        print("\n💥 API test failed!")
