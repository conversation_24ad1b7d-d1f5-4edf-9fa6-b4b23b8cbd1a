//! SMC Pattern Detection Algorithms
//! 
//! This module implements the core Smart Money Concept pattern detection:
//! - Fair Value Gap (FVG) detection
//! - Order Block (OB) identification
//! - Imbalance detection

use crate::Candle;
use super::{SmcPattern, SmcPatternType, SmcDirection};
use uuid::Uuid;

/// Fair Value Gap (FVG) Detector
pub struct FvgDetector {
    pub min_gap_percentage: f64, // Minimum gap size as percentage of price
    pub min_volume_ratio: f64,   // Minimum volume ratio for validation
}

impl FvgDetector {
    pub fn new() -> Self {
        Self {
            min_gap_percentage: 0.1, // 0.1% minimum gap
            min_volume_ratio: 1.5,   // 1.5x average volume
        }
    }

    /// Detect Fair Value Gaps in candle data
    pub fn detect_fvg(&self, candles: &[Candle]) -> Vec<SmcPattern> {
        let mut fvg_patterns = Vec::new();
        
        if candles.len() < 3 {
            return fvg_patterns;
        }

        // Look for 3-candle FVG patterns
        for i in 1..candles.len() - 1 {
            let prev_candle = &candles[i - 1];
            let current_candle = &candles[i];
            let next_candle = &candles[i + 1];

            // Check for Bullish FVG
            if let Some(bullish_fvg) = self.detect_bullish_fvg(prev_candle, current_candle, next_candle, i) {
                fvg_patterns.push(bullish_fvg);
            }

            // Check for Bearish FVG
            if let Some(bearish_fvg) = self.detect_bearish_fvg(prev_candle, current_candle, next_candle, i) {
                fvg_patterns.push(bearish_fvg);
            }
        }

        fvg_patterns
    }

    /// Detect Bullish Fair Value Gap
    fn detect_bullish_fvg(
        &self,
        prev_candle: &Candle,
        current_candle: &Candle,
        next_candle: &Candle,
        index: usize,
    ) -> Option<SmcPattern> {
        // Bullish FVG: prev_candle.high < next_candle.low
        // The current candle should be a strong bullish candle
        
        let gap_exists = prev_candle.high < next_candle.low;
        let strong_bullish = current_candle.close > current_candle.open && 
                           (current_candle.close - current_candle.open) / current_candle.open > 0.005; // 0.5% body
        
        if gap_exists && strong_bullish {
            let gap_size = next_candle.low - prev_candle.high;
            let gap_percentage = gap_size / prev_candle.high * 100.0;
            
            if gap_percentage >= self.min_gap_percentage {
                let confidence = self.calculate_fvg_confidence(prev_candle, current_candle, next_candle);
                
                return Some(SmcPattern {
                    id: Uuid::new_v4().to_string(),
                    pattern_type: SmcPatternType::FairValueGap,
                    direction: SmcDirection::Bullish,
                    symbol: current_candle.symbol.clone(),
                    timeframe: current_candle.timerange.clone(),
                    timestamp: current_candle.timestamp,
                    start_price: prev_candle.high,
                    end_price: next_candle.low,
                    high: next_candle.low,
                    low: prev_candle.high,
                    confidence,
                    volume: current_candle.volume,
                    candles_involved: vec![index - 1, index, index + 1],
                });
            }
        }

        None
    }

    /// Detect Bearish Fair Value Gap
    fn detect_bearish_fvg(
        &self,
        prev_candle: &Candle,
        current_candle: &Candle,
        next_candle: &Candle,
        index: usize,
    ) -> Option<SmcPattern> {
        // Bearish FVG: prev_candle.low > next_candle.high
        // The current candle should be a strong bearish candle
        
        let gap_exists = prev_candle.low > next_candle.high;
        let strong_bearish = current_candle.close < current_candle.open && 
                           (current_candle.open - current_candle.close) / current_candle.open > 0.005; // 0.5% body
        
        if gap_exists && strong_bearish {
            let gap_size = prev_candle.low - next_candle.high;
            let gap_percentage = gap_size / prev_candle.low * 100.0;
            
            if gap_percentage >= self.min_gap_percentage {
                let confidence = self.calculate_fvg_confidence(prev_candle, current_candle, next_candle);
                
                return Some(SmcPattern {
                    id: Uuid::new_v4().to_string(),
                    pattern_type: SmcPatternType::FairValueGap,
                    direction: SmcDirection::Bearish,
                    symbol: current_candle.symbol.clone(),
                    timeframe: current_candle.timerange.clone(),
                    timestamp: current_candle.timestamp,
                    start_price: prev_candle.low,
                    end_price: next_candle.high,
                    high: prev_candle.low,
                    low: next_candle.high,
                    confidence,
                    volume: current_candle.volume,
                    candles_involved: vec![index - 1, index, index + 1],
                });
            }
        }

        None
    }

    /// Calculate FVG confidence score
    fn calculate_fvg_confidence(&self, prev: &Candle, current: &Candle, next: &Candle) -> f64 {
        let mut confidence: f64 = 0.5; // Base confidence
        
        // Volume confirmation
        let avg_volume = (prev.volume + current.volume + next.volume) / 3.0;
        if current.volume > avg_volume * self.min_volume_ratio {
            confidence += 0.2;
        }
        
        // Body size confirmation
        let current_body_size = (current.close - current.open).abs() / current.open;
        if current_body_size > 0.01 { // 1% body size
            confidence += 0.2;
        }
        
        // Gap size confirmation
        let gap_size = if prev.high < next.low {
            next.low - prev.high
        } else {
            prev.low - next.high
        };
        let gap_percentage = gap_size / prev.close * 100.0;
        
        if gap_percentage > 0.5 {
            confidence += 0.1;
        }
        
        confidence.min(1.0)
    }
}

/// Order Block Detector
pub struct OrderBlockDetector {
    pub min_block_size: f64,     // Minimum order block size
    pub volume_threshold: f64,   // Volume threshold for order blocks
}

impl OrderBlockDetector {
    pub fn new() -> Self {
        Self {
            min_block_size: 0.002, // 0.2% minimum block size
            volume_threshold: 1.5,  // 1.5x average volume
        }
    }

    /// Detect Order Blocks in candle data
    pub fn detect_order_blocks(&self, candles: &[Candle]) -> Vec<SmcPattern> {
        let mut order_blocks = Vec::new();
        
        if candles.len() < 5 {
            return order_blocks;
        }

        // Look for order block patterns
        for i in 2..candles.len() - 2 {
            // Check for Bullish Order Block
            if let Some(bullish_ob) = self.detect_bullish_order_block(candles, i) {
                order_blocks.push(bullish_ob);
            }

            // Check for Bearish Order Block
            if let Some(bearish_ob) = self.detect_bearish_order_block(candles, i) {
                order_blocks.push(bearish_ob);
            }
        }

        order_blocks
    }

    /// Detect Bullish Order Block
    fn detect_bullish_order_block(&self, candles: &[Candle], index: usize) -> Option<SmcPattern> {
        let current = &candles[index];
        
        // Look for a strong bullish candle with high volume
        let is_bullish = current.close > current.open;
        let body_size = (current.close - current.open) / current.open;
        let has_strong_body = body_size >= self.min_block_size;
        
        if is_bullish && has_strong_body {
            // Check volume confirmation
            let avg_volume = self.calculate_average_volume(candles, index, 5);
            let volume_confirmed = current.volume >= avg_volume * self.volume_threshold;
            
            if volume_confirmed {
                let confidence = self.calculate_order_block_confidence(candles, index);
                
                return Some(SmcPattern {
                    id: Uuid::new_v4().to_string(),
                    pattern_type: SmcPatternType::OrderBlock,
                    direction: SmcDirection::Bullish,
                    symbol: current.symbol.clone(),
                    timeframe: current.timerange.clone(),
                    timestamp: current.timestamp,
                    start_price: current.open,
                    end_price: current.close,
                    high: current.high,
                    low: current.low,
                    confidence,
                    volume: current.volume,
                    candles_involved: vec![index],
                });
            }
        }

        None
    }

    /// Detect Bearish Order Block
    fn detect_bearish_order_block(&self, candles: &[Candle], index: usize) -> Option<SmcPattern> {
        let current = &candles[index];
        
        // Look for a strong bearish candle with high volume
        let is_bearish = current.close < current.open;
        let body_size = (current.open - current.close) / current.open;
        let has_strong_body = body_size >= self.min_block_size;
        
        if is_bearish && has_strong_body {
            // Check volume confirmation
            let avg_volume = self.calculate_average_volume(candles, index, 5);
            let volume_confirmed = current.volume >= avg_volume * self.volume_threshold;
            
            if volume_confirmed {
                let confidence = self.calculate_order_block_confidence(candles, index);
                
                return Some(SmcPattern {
                    id: Uuid::new_v4().to_string(),
                    pattern_type: SmcPatternType::OrderBlock,
                    direction: SmcDirection::Bearish,
                    symbol: current.symbol.clone(),
                    timeframe: current.timerange.clone(),
                    timestamp: current.timestamp,
                    start_price: current.open,
                    end_price: current.close,
                    high: current.high,
                    low: current.low,
                    confidence,
                    volume: current.volume,
                    candles_involved: vec![index],
                });
            }
        }

        None
    }

    /// Calculate average volume over a period
    fn calculate_average_volume(&self, candles: &[Candle], index: usize, period: usize) -> f64 {
        let start = if index >= period { index - period } else { 0 };
        let end = (index + 1).min(candles.len());
        
        let sum: f64 = candles[start..end].iter().map(|c| c.volume).sum();
        sum / (end - start) as f64
    }

    /// Calculate Order Block confidence score
    fn calculate_order_block_confidence(&self, candles: &[Candle], index: usize) -> f64 {
        let current = &candles[index];
        let mut confidence: f64 = 0.6; // Base confidence
        
        // Volume confirmation
        let avg_volume = self.calculate_average_volume(candles, index, 10);
        let volume_ratio = current.volume / avg_volume;
        
        if volume_ratio > 2.0 {
            confidence += 0.2;
        } else if volume_ratio > 1.5 {
            confidence += 0.1;
        }
        
        // Body size confirmation
        let body_size = (current.close - current.open).abs() / current.open;
        if body_size > 0.02 { // 2% body size
            confidence += 0.2;
        }
        
        confidence.min(1.0)
    }
}

impl Default for FvgDetector {
    fn default() -> Self {
        Self::new()
    }
}

impl Default for OrderBlockDetector {
    fn default() -> Self {
        Self::new()
    }
}
