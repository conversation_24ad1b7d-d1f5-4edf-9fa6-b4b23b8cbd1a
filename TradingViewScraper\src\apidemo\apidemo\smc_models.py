"""
Smart Money Concept (SMC) Models for TradingView Integration
"""
from pydantic import BaseModel
from enum import Enum
from datetime import datetime
from typing import List, Optional


class SmcPatternType(str, Enum):
    FAIR_VALUE_GAP = "Fair Value Gap"
    ORDER_BLOCK = "Order Block"
    CHANGE_OF_CHARACTER = "Change of Character"
    BREAK_OF_STRUCTURE = "Break of Structure"
    LIQUIDITY_ZONE = "Liquidity Zone"


class SmcDirection(str, Enum):
    BULLISH = "Bullish"
    BEARISH = "Bearish"


class LiquidityType(str, Enum):
    SUPPORT = "Support"
    RESISTANCE = "Resistance"
    ORDER_BLOCK = "Order Block"
    FAIR_VALUE_GAP = "Fair Value Gap"


class SignalType(str, Enum):
    FVG_ENTRY = "FVG Entry"
    ORDER_BLOCK_ENTRY = "Order Block Entry"
    STRUCTURE_BREAK = "Structure Break"
    LIQUIDITY_GRAB = "Liquidity Grab"
    TREND_CONTINUATION = "Trend Continuation"
    TREND_REVERSAL = "Trend Reversal"


class CandleData(BaseModel):
    """Candle data structure for SMC analysis"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float


class SmcPattern(BaseModel):
    """SMC Pattern Detection Result"""
    id: str
    pattern_type: SmcPatternType
    direction: SmcDirection
    symbol: str
    timeframe: str
    timestamp: datetime
    start_price: float
    end_price: float
    high: float
    low: float
    confidence: float  # 0.0 to 1.0
    volume: float
    candles_involved: List[int]  # Indices of candles that form this pattern


class LiquidityLevel(BaseModel):
    """Liquidity Level Identification"""
    price: float
    strength: float  # 0.0 to 1.0
    level_type: LiquidityType
    timestamp: datetime


class MarketStructure(BaseModel):
    """Market Structure Analysis"""
    trend: SmcDirection
    strength: float  # 0.0 to 1.0
    last_choch: Optional[SmcPattern] = None
    last_bos: Optional[SmcPattern] = None
    higher_highs: List[float]
    higher_lows: List[float]
    lower_highs: List[float]
    lower_lows: List[float]


class SmcTradingSignal(BaseModel):
    """SMC Trading Signal"""
    signal_type: SignalType
    direction: SmcDirection
    strength: float  # 0.0 to 1.0
    entry_price: float
    stop_loss: float
    take_profit: float
    risk_reward_ratio: float
    patterns_involved: List[str]  # Pattern IDs
    confidence: float


class SmcAnalysisResult(BaseModel):
    """Complete SMC Analysis Result"""
    symbol: str
    timeframe: str
    timestamp: datetime
    patterns: List[SmcPattern]
    market_structure: MarketStructure
    liquidity_levels: List[LiquidityLevel]
    trading_signals: List[SmcTradingSignal]


class SmcRequest(BaseModel):
    """Request for SMC Analysis"""
    pairs: List[str]
    intervals: List[str]
    include_signals: bool = True
    min_confidence: float = 0.7


class SmcResponse(BaseModel):
    """SMC Analysis Response"""
    success: bool
    message: str
    timestamp: datetime
    results: dict[str, List[SmcAnalysisResult]]  # pair -> list of timeframe results
