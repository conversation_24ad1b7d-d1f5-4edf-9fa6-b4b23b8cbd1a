#!/usr/bin/env python3
"""
Mock TradingView API Server for testing Rust integration
This simulates your actual TradingView scraper API
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uvicorn
from datetime import datetime
import random

app = FastAPI(title="TradingView Scraper API", version="1.0.0")

# Data models matching your scraper structure
class Interval(BaseModel):
    value: str

class PairRequest(BaseModel):
    pairs: List[str]
    intervals: List[Interval]

class IndicatorDTO(BaseModel):
    pair: str
    interval: str
    register_time: str
    name: str
    value: Optional[float]
    action: str

class PivotDTO(BaseModel):
    pair: str
    interval: str
    register_time: str
    pivot: str
    classic: Optional[float]
    fibo: Optional[float]
    camarilla: Optional[float]
    woodie: Optional[float]
    dm: Optional[float]

class FinancialDTO(BaseModel):
    pair: str
    price: float
    oscillators: List[IndicatorDTO]
    moving_averages: List[IndicatorDTO]
    pivots: List[PivotDTO]

# Mock data for different symbols
MOCK_PRICES = {
    "EGX:ETEL": 15.50,
    "EGX:CIB": 45.20,
    "EGX:HERM": 8.75,
    "EGX:SWDY": 12.30,
    "EGX:ORWE": 6.80,
    "FOREX:EURUSD": 1.0850,
    "FOREX:GBPUSD": 1.2650,
}

def generate_mock_indicators(pair: str, interval: str) -> List[IndicatorDTO]:
    """Generate mock technical indicators"""
    indicators = []
    indicator_names = ["RSI", "MACD", "Stochastic", "Williams %R", "CCI"]
    
    for name in indicator_names:
        indicators.append(IndicatorDTO(
            pair=pair,
            interval=interval,
            register_time=datetime.now().strftime("%d/%m/%Y %H:%M"),
            name=name,
            value=round(random.uniform(20, 80), 2),
            action=random.choice(["BUY", "SELL", "NEUTRAL"])
        ))
    
    return indicators

def generate_mock_moving_averages(pair: str, interval: str) -> List[IndicatorDTO]:
    """Generate mock moving averages"""
    mas = []
    ma_names = ["EMA10", "EMA20", "SMA50", "SMA200"]
    
    for name in ma_names:
        mas.append(IndicatorDTO(
            pair=pair,
            interval=interval,
            register_time=datetime.now().strftime("%d/%m/%Y %H:%M"),
            name=name,
            value=round(random.uniform(10, 50), 4),
            action=random.choice(["BUY", "SELL", "NEUTRAL"])
        ))
    
    return mas

def generate_mock_pivots(pair: str, interval: str) -> List[PivotDTO]:
    """Generate mock pivot points"""
    pivots = []
    pivot_types = ["R3", "R2", "R1", "PP", "S1", "S2", "S3"]
    
    for pivot_type in pivot_types:
        pivots.append(PivotDTO(
            pair=pair,
            interval=interval,
            register_time=datetime.now().strftime("%d/%m/%Y %H:%M"),
            pivot=pivot_type,
            classic=round(random.uniform(10, 50), 4),
            fibo=round(random.uniform(10, 50), 4),
            camarilla=round(random.uniform(10, 50), 4),
            woodie=round(random.uniform(10, 50), 4),
            dm=round(random.uniform(10, 50), 4)
        ))
    
    return pivots

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "TradingView Scraper API",
        "status": "running",
        "endpoints": {
            "scrape_pairs": "/api/scrape_pairs",
            "docs": "/docs"
        }
    }

@app.post("/api/scrape_pairs")
async def scrape_pairs(request: PairRequest) -> Dict[str, List[FinancialDTO]]:
    """
    Main endpoint that mimics your TradingView scraper
    """
    print(f"📊 Received request for pairs: {request.pairs}")
    print(f"📊 Intervals: {[i.value for i in request.intervals]}")
    
    result = {}
    
    for pair in request.pairs:
        print(f"🔄 Processing pair: {pair}")
        
        # Get base price for the symbol
        base_price = MOCK_PRICES.get(pair, 100.0)
        # Add some random variation
        current_price = round(base_price * random.uniform(0.98, 1.02), 4)
        
        pair_data = []
        
        for interval in request.intervals:
            financial_data = FinancialDTO(
                pair=pair,
                price=current_price,
                oscillators=generate_mock_indicators(pair, interval.value),
                moving_averages=generate_mock_moving_averages(pair, interval.value),
                pivots=generate_mock_pivots(pair, interval.value)
            )
            pair_data.append(financial_data)
        
        result[pair] = pair_data
        print(f"✅ Generated data for {pair}: price={current_price}")
    
    return result

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    print("🚀 Starting TradingView Mock API Server...")
    print("📡 Server will be available at: http://127.0.0.1:8000")
    print("📖 API docs will be available at: http://127.0.0.1:8000/docs")
    print("🧪 This is a MOCK server for testing Rust integration")
    
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
