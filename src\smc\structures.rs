//! SMC Market Structure Analysis
//! 
//! This module implements market structure analysis for Smart Money Concepts:
//! - Change of Character (CHoCH) detection
//! - Break of Structure (BOS) detection
//! - Market trend analysis
//! - Support and resistance levels

use crate::Candle;
use super::{SmcPattern, SmcPatternType, SmcDirection, MarketStructure, LiquidityLevel, LiquidityType};
use uuid::Uuid;

/// Structure Analysis Result
pub struct StructureAnalysisResult {
    pub market_structure: MarketStructure,
    pub liquidity_levels: Vec<LiquidityLevel>,
    pub choch_patterns: Vec<SmcPattern>,
    pub bos_patterns: Vec<SmcPattern>,
}

/// Market Structure Analyzer
pub struct StructureAnalyzer {
    pub swing_threshold: f64,    // Minimum percentage for swing identification
    pub volume_confirmation: f64, // Volume threshold for structure breaks
}

impl StructureAnalyzer {
    pub fn new() -> Self {
        Self {
            swing_threshold: 0.5,   // 0.5% minimum swing
            volume_confirmation: 1.2, // 1.2x average volume
        }
    }

    /// Analyze market structure from candle data
    pub fn analyze_structure(&self, candles: &[Candle]) -> StructureAnalysisResult {
        if candles.len() < 10 {
            return StructureAnalysisResult {
                market_structure: MarketStructure {
                    trend: SmcDirection::Bullish,
                    strength: 0.0,
                    last_choch: None,
                    last_bos: None,
                    higher_highs: vec![],
                    higher_lows: vec![],
                    lower_highs: vec![],
                    lower_lows: vec![],
                },
                liquidity_levels: vec![],
                choch_patterns: vec![],
                bos_patterns: vec![],
            };
        }

        // Identify swing points
        let swing_points = self.identify_swing_points(candles);
        
        // Analyze trend structure
        let market_structure = self.analyze_trend_structure(candles, &swing_points);
        
        // Detect CHoCH and BOS patterns
        let choch_patterns = self.detect_choch_patterns(candles, &swing_points);
        let bos_patterns = self.detect_bos_patterns(candles, &swing_points);
        
        // Identify liquidity levels
        let liquidity_levels = self.identify_liquidity_levels(candles, &swing_points);

        StructureAnalysisResult {
            market_structure,
            liquidity_levels,
            choch_patterns,
            bos_patterns,
        }
    }

    /// Identify swing high and low points
    fn identify_swing_points(&self, candles: &[Candle]) -> Vec<SwingPoint> {
        let mut swing_points = Vec::new();
        
        if candles.len() < 5 {
            return swing_points;
        }

        // Look for swing highs and lows using a 5-period window
        for i in 2..candles.len() - 2 {
            let current = &candles[i];
            
            // Check for swing high
            if self.is_swing_high(candles, i) {
                swing_points.push(SwingPoint {
                    index: i,
                    price: current.high,
                    swing_type: SwingType::High,
                    timestamp: current.timestamp,
                });
            }
            
            // Check for swing low
            if self.is_swing_low(candles, i) {
                swing_points.push(SwingPoint {
                    index: i,
                    price: current.low,
                    swing_type: SwingType::Low,
                    timestamp: current.timestamp,
                });
            }
        }

        swing_points
    }

    /// Check if a candle forms a swing high
    fn is_swing_high(&self, candles: &[Candle], index: usize) -> bool {
        if index < 2 || index >= candles.len() - 2 {
            return false;
        }

        let current_high = candles[index].high;
        
        // Check if current high is higher than surrounding candles
        for i in (index - 2)..=(index + 2) {
            if i != index && candles[i].high >= current_high {
                return false;
            }
        }

        // Check if the swing is significant enough
        let prev_low = candles[index - 2..=index + 2].iter().map(|c| c.low).fold(f64::INFINITY, f64::min);
        let swing_size = (current_high - prev_low) / prev_low * 100.0;
        
        swing_size >= self.swing_threshold
    }

    /// Check if a candle forms a swing low
    fn is_swing_low(&self, candles: &[Candle], index: usize) -> bool {
        if index < 2 || index >= candles.len() - 2 {
            return false;
        }

        let current_low = candles[index].low;
        
        // Check if current low is lower than surrounding candles
        for i in (index - 2)..=(index + 2) {
            if i != index && candles[i].low <= current_low {
                return false;
            }
        }

        // Check if the swing is significant enough
        let prev_high = candles[index - 2..=index + 2].iter().map(|c| c.high).fold(0.0, f64::max);
        let swing_size = (prev_high - current_low) / current_low * 100.0;
        
        swing_size >= self.swing_threshold
    }

    /// Analyze trend structure based on swing points
    fn analyze_trend_structure(&self, _candles: &[Candle], swing_points: &[SwingPoint]) -> MarketStructure {
        let mut higher_highs = Vec::new();
        let mut higher_lows = Vec::new();
        let mut lower_highs = Vec::new();
        let mut lower_lows = Vec::new();

        // Separate swing highs and lows
        let swing_highs: Vec<&SwingPoint> = swing_points.iter()
            .filter(|sp| matches!(sp.swing_type, SwingType::High))
            .collect();
        let swing_lows: Vec<&SwingPoint> = swing_points.iter()
            .filter(|sp| matches!(sp.swing_type, SwingType::Low))
            .collect();

        // Analyze swing highs for higher highs and lower highs
        for i in 1..swing_highs.len() {
            let current_high = swing_highs[i].price;
            let previous_high = swing_highs[i - 1].price;
            
            if current_high > previous_high {
                higher_highs.push(current_high);
            } else {
                lower_highs.push(current_high);
            }
        }

        // Analyze swing lows for higher lows and lower lows
        for i in 1..swing_lows.len() {
            let current_low = swing_lows[i].price;
            let previous_low = swing_lows[i - 1].price;
            
            if current_low > previous_low {
                higher_lows.push(current_low);
            } else {
                lower_lows.push(current_low);
            }
        }

        // Determine overall trend
        let bullish_signals = higher_highs.len() + higher_lows.len();
        let bearish_signals = lower_highs.len() + lower_lows.len();
        
        let trend = if bullish_signals > bearish_signals {
            SmcDirection::Bullish
        } else {
            SmcDirection::Bearish
        };

        // Calculate trend strength
        let total_signals = bullish_signals + bearish_signals;
        let strength = if total_signals > 0 {
            (bullish_signals as f64 - bearish_signals as f64).abs() / total_signals as f64
        } else {
            0.0
        };

        MarketStructure {
            trend,
            strength,
            last_choch: None, // Will be filled by CHoCH detection
            last_bos: None,   // Will be filled by BOS detection
            higher_highs,
            higher_lows,
            lower_highs,
            lower_lows,
        }
    }

    /// Detect Change of Character (CHoCH) patterns
    fn detect_choch_patterns(&self, candles: &[Candle], swing_points: &[SwingPoint]) -> Vec<SmcPattern> {
        let mut choch_patterns = Vec::new();
        
        if swing_points.len() < 4 {
            return choch_patterns;
        }

        // Look for trend reversals in swing points
        for i in 3..swing_points.len() {
            if let Some(choch) = self.identify_choch(&swing_points[i-3..=i], candles) {
                choch_patterns.push(choch);
            }
        }

        choch_patterns
    }

    /// Identify a CHoCH pattern from swing points
    fn identify_choch(&self, swing_sequence: &[SwingPoint], candles: &[Candle]) -> Option<SmcPattern> {
        if swing_sequence.len() != 4 {
            return None;
        }

        // Pattern: High-Low-High-Low or Low-High-Low-High with trend change
        let sp1 = &swing_sequence[0];
        let sp2 = &swing_sequence[1];
        let sp3 = &swing_sequence[2];
        let sp4 = &swing_sequence[3];

        // Check for bullish CHoCH: LL-LH-HL-HH (trend change from bearish to bullish)
        if matches!(sp1.swing_type, SwingType::Low) &&
           matches!(sp2.swing_type, SwingType::High) &&
           matches!(sp3.swing_type, SwingType::Low) &&
           matches!(sp4.swing_type, SwingType::High) {
            
            // Check if we have a trend change: higher low and higher high
            if sp3.price > sp1.price && sp4.price > sp2.price {
                let candle = &candles[sp4.index];
                return Some(SmcPattern {
                    id: Uuid::new_v4().to_string(),
                    pattern_type: SmcPatternType::ChangeOfCharacter,
                    direction: SmcDirection::Bullish,
                    symbol: candle.symbol.clone(),
                    timeframe: candle.timerange.clone(),
                    timestamp: sp4.timestamp,
                    start_price: sp1.price,
                    end_price: sp4.price,
                    high: sp4.price,
                    low: sp1.price,
                    confidence: 0.8,
                    volume: candle.volume,
                    candles_involved: vec![sp1.index, sp2.index, sp3.index, sp4.index],
                });
            }
        }

        // Check for bearish CHoCH: HH-HL-LH-LL (trend change from bullish to bearish)
        if matches!(sp1.swing_type, SwingType::High) &&
           matches!(sp2.swing_type, SwingType::Low) &&
           matches!(sp3.swing_type, SwingType::High) &&
           matches!(sp4.swing_type, SwingType::Low) {
            
            // Check if we have a trend change: lower high and lower low
            if sp3.price < sp1.price && sp4.price < sp2.price {
                let candle = &candles[sp4.index];
                return Some(SmcPattern {
                    id: Uuid::new_v4().to_string(),
                    pattern_type: SmcPatternType::ChangeOfCharacter,
                    direction: SmcDirection::Bearish,
                    symbol: candle.symbol.clone(),
                    timeframe: candle.timerange.clone(),
                    timestamp: sp4.timestamp,
                    start_price: sp1.price,
                    end_price: sp4.price,
                    high: sp1.price,
                    low: sp4.price,
                    confidence: 0.8,
                    volume: candle.volume,
                    candles_involved: vec![sp1.index, sp2.index, sp3.index, sp4.index],
                });
            }
        }

        None
    }

    /// Detect Break of Structure (BOS) patterns
    fn detect_bos_patterns(&self, _candles: &[Candle], _swing_points: &[SwingPoint]) -> Vec<SmcPattern> {
        let bos_patterns = Vec::new();
        
        // BOS detection logic would go here
        // This is a simplified version - in practice, BOS detection is more complex
        
        bos_patterns
    }

    /// Identify liquidity levels from swing points
    fn identify_liquidity_levels(&self, candles: &[Candle], swing_points: &[SwingPoint]) -> Vec<LiquidityLevel> {
        let mut liquidity_levels = Vec::new();
        
        for swing_point in swing_points {
            let level_type = match swing_point.swing_type {
                SwingType::High => LiquidityType::Resistance,
                SwingType::Low => LiquidityType::Support,
            };
            
            // Calculate strength based on how many times the level was tested
            let strength = self.calculate_level_strength(candles, swing_point.price);
            
            liquidity_levels.push(LiquidityLevel {
                price: swing_point.price,
                strength,
                level_type,
                timestamp: swing_point.timestamp,
            });
        }

        liquidity_levels
    }

    /// Calculate the strength of a liquidity level
    fn calculate_level_strength(&self, candles: &[Candle], level_price: f64) -> f64 {
        let tolerance = level_price * 0.001; // 0.1% tolerance
        let mut touches = 0;
        
        for candle in candles {
            if (candle.high - level_price).abs() <= tolerance || 
               (candle.low - level_price).abs() <= tolerance {
                touches += 1;
            }
        }
        
        // Normalize strength between 0.0 and 1.0
        (touches as f64 / 10.0).min(1.0)
    }
}

/// Swing Point Structure
#[derive(Debug, Clone)]
pub struct SwingPoint {
    pub index: usize,
    pub price: f64,
    pub swing_type: SwingType,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Swing Point Type
#[derive(Debug, Clone)]
pub enum SwingType {
    High,
    Low,
}

impl Default for StructureAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}
