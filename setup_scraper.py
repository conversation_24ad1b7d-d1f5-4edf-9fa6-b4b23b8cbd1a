#!/usr/bin/env python3
"""
Setup script for TradingView scraper dependencies
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False

def main():
    print("🚀 Setting up TradingView scraper for Paragon SMC platform")
    print("=" * 60)
    
    # Check if Python is available
    try:
        python_version = subprocess.check_output([sys.executable, "--version"], text=True).strip()
        print(f"✅ Python found: {python_version}")
    except Exception as e:
        print(f"❌ Python not found: {e}")
        return False
    
    # Install requirements
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing Python dependencies"):
        return False
    
    # Install Playwright browsers
    if not run_command("playwright install chromium", "Installing Playwright Chromium browser"):
        return False
    
    # Test the scraper
    print("\n🧪 Testing the scraper...")
    test_command = f'{sys.executable} src/data_sources/tradingview_scraper.py \'["EGX-COMI"]\' \'["1d"]\''
    
    try:
        result = subprocess.run(test_command, shell=True, capture_output=True, text=True, timeout=60)
        if "RESULT_START" in result.stdout and "RESULT_END" in result.stdout:
            print("✅ Scraper test successful!")
            print("🎉 Setup completed! You can now run Paragon with TradingView integration.")
            return True
        else:
            print("❌ Scraper test failed - no valid output")
            print(f"   stdout: {result.stdout}")
            print(f"   stderr: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Scraper test timed out")
        return False
    except Exception as e:
        print(f"❌ Scraper test error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 Setup failed! Please check the errors above.")
        sys.exit(1)
    else:
        print("\n🎯 Next steps:")
        print("   1. Run: cargo run --bin main_with_tradingview")
        print("   2. Or set DATA_SOURCE=tradingview and run: cargo run")
