#!/usr/bin/env python3
"""
Direct TradingView scraper for Paragon SMC platform
Extracted from your TradingView scraper app
"""

import asyncio
import json
import sys
from datetime import datetime
from typing import List, Dict, Any, Optional
from playwright.async_api import async_playwright, Page
import re


def to_float(value: str) -> Optional[float]:
    """Convert string to float, handling None and invalid values"""
    if not value or value == "—" or value == "-":
        return None
    try:
        # Remove any non-numeric characters except decimal point and minus
        cleaned = re.sub(r'[^\d.-]', '', value)
        return float(cleaned) if cleaned else None
    except (ValueError, TypeError):
        return None


async def fetch_price(page: Page) -> Optional[float]:
    """Extract current price from TradingView page"""
    try:
        price_selector = ".lastContainer-zoF9r75I"
        await page.wait_for_selector(price_selector, timeout=20000)
        price_element = page.locator(price_selector).first
        price_text = await price_element.text_content()
        
        if price_text:
            # Clean the price text and extract numeric value
            cleaned_price = re.sub(r'[^\d.]', '', price_text)
            return float(cleaned_price) if cleaned_price else None
    except Exception as e:
        print(f"Error fetching price: {e}")
    return None


async def fetch_indicators(page: Page, css_selector: str, interval: str, pair: str) -> List[Dict[str, Any]]:
    """Extract technical indicators from TradingView page"""
    try:
        await page.wait_for_selector(css_selector, timeout=10000)
        elements = await page.locator(css_selector).all()
        indicator_list = []

        for i in range(0, len(elements), 3):
            if i + 2 < len(elements):
                name = await elements[i].text_content()
                value_text = await elements[i + 1].text_content()
                action = await elements[i + 2].text_content()
                
                indicator_list.append({
                    "pair": pair,
                    "interval": interval,
                    "register_time": datetime.now().strftime("%d/%m/%Y %H:%M"),
                    "name": name,
                    "value": to_float(value_text),
                    "action": action,
                })
        
        return indicator_list
    except Exception as e:
        print(f"Error fetching indicators: {e}")
        return []


async def fetch_pivots(page: Page, css_selector: str, interval: str, pair: str) -> List[Dict[str, Any]]:
    """Extract pivot points from TradingView page"""
    try:
        await page.wait_for_selector(css_selector, timeout=10000)
        elements = await page.locator(css_selector).all()
        pivot_list = []

        for i in range(0, len(elements), 6):
            if i + 5 < len(elements):
                pivot_name = await elements[i].text_content()
                classic = await elements[i + 1].text_content()
                fibo = await elements[i + 2].text_content()
                camarilla = await elements[i + 3].text_content()
                woodie = await elements[i + 4].text_content()
                dm = await elements[i + 5].text_content()
                
                pivot_list.append({
                    "pair": pair,
                    "interval": interval,
                    "register_time": datetime.now().strftime("%d/%m/%Y %H:%M"),
                    "pivot": pivot_name,
                    "classic": to_float(classic),
                    "fibo": to_float(fibo),
                    "camarilla": to_float(camarilla),
                    "woodie": to_float(woodie),
                    "dm": to_float(dm),
                })
        
        return pivot_list
    except Exception as e:
        print(f"Error fetching pivots: {e}")
        return []


async def scrape_pair(pair: str, page: Page, intervals: List[str]) -> List[Dict[str, Any]]:
    """Scrape a single trading pair for all specified intervals"""
    url = f"https://tradingview.com/symbols/{pair}/technicals/"
    all_times_payload = []
    
    try:
        await page.goto(url, wait_until="domcontentloaded", timeout=30000)
        
        # CSS selectors for different data types
        oscillator_selector = "div:nth-child(1)> div.tableWrapper-hvDpy38G > table > tbody > tr.row-hvDpy38G > *"
        moving_avg_selector = "div.container-hvDpy38G.maTable-kg4MJrFB.tableWithAction-kg4MJrFB.tabletVertical-kg4MJrFB.tabletVertical-hvDpy38G > div.tableWrapper-hvDpy38G > table > tbody > tr.row-hvDpy38G > td"
        pivot_selector = "div.container-hvDpy38G.tabletVertical-hvDpy38G > div.container-Tv7LSjUz > div.wrapper-Tv7LSjUz > div > table > tbody > tr.row-hvDpy38G > td"

        for interval in intervals:
            try:
                # Click on the interval button
                await page.wait_for_selector(f'button[id="{interval}"]', timeout=10000)
                await page.click(f'button[id="{interval}"]', timeout=5000)
                
                # Wait a bit for data to load
                await page.wait_for_timeout(2000)

                # Fetch all data types
                price = await fetch_price(page)
                oscillators = await fetch_indicators(page, oscillator_selector, interval, pair)
                moving_averages = await fetch_indicators(page, moving_avg_selector, interval, pair)
                pivots = await fetch_pivots(page, pivot_selector, interval, pair)

                asset_data = {
                    "pair": pair,
                    "price": price,
                    "oscillators": oscillators,
                    "moving_averages": moving_averages,
                    "pivots": pivots,
                }
                
                all_times_payload.append(asset_data)
                print(f"Scraped {pair} for {interval}: Price={price}")
                
            except Exception as e:
                print(f"Error scraping {pair} for interval {interval}: {e}")
                continue

    except Exception as e:
        print(f"Error accessing {url}: {e}")

    return all_times_payload


async def main():
    """Main scraper function - called from Rust"""
    try:
        # Handle arguments with defaults for testing
        if len(sys.argv) >= 3:
            pairs_arg = sys.argv[1]
            intervals_arg = sys.argv[2]
        else:
            # Default values for testing
            pairs_arg = '["EGX-COMI"]'
            intervals_arg = '["1d"]'
            print("Using default test values: EGX-COMI, 1d")

        print(f"Parsing pairs: {pairs_arg}")
        print(f"Parsing intervals: {intervals_arg}")

        pairs = json.loads(pairs_arg)
        intervals = json.loads(intervals_arg)

        print(f"Starting TradingView scraper for {len(pairs)} pairs, {len(intervals)} intervals")
        
        result = {}
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            # Set user agent to avoid detection
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            })
            
            try:
                for pair in pairs:
                    print(f"Scraping {pair}...")
                    pair_data = await scrape_pair(pair, page, intervals)
                    result[pair] = pair_data

                    # Small delay between pairs to avoid rate limiting
                    await asyncio.sleep(1)
                    
            finally:
                await browser.close()
        
        # Output result as JSON for Rust to parse
        print("RESULT_START")
        print(json.dumps({"result": result}, indent=2))
        print("RESULT_END")
        
    except Exception as e:
        print(f"Scraper error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
