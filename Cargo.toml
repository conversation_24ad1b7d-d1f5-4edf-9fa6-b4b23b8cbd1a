[package]
name = "paragon"
version = "0.1.0"
edition = "2021"

[dependencies]
chrono = { version = "0.4.41", features = ["serde"] }
dashmap = "6.1.0"
deadpool-postgres = "0.14.1"
futures = "0.3.31"
once_cell = "1.21.3"
polars = { version = "0.48.1", features = ["parquet", "timezones"] }
tokio = {version = "1.45.1" , features = ["full"] }
tokio-postgres = { version = "0.7.13", features = ["with-chrono-0_4"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
reqwest = { version = "0.11", features = ["json"] }
thiserror = "1.0"
async-trait = "0.1"
toml = "0.8"
uuid = { version = "1.0", features = ["v4"] }

[[bin]]
name = "smc"
path = "src/main_smc.rs"
