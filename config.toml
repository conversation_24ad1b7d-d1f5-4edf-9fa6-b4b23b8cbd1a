# Paragon Trading Platform Configuration

[database]
host = "localhost"
port = 5432
user = "enzoblain"
# password = "your_password"  # Uncomment and set if needed
dbname = "Paragon"
max_connections = 10

[tradingview]
api_base_url = "http://127.0.0.1:8000"
max_concurrent_requests = 5
rate_limit_delay_ms = 500
enabled = true

[monitoring]
# EGX stocks to monitor
symbols = [
    "EGX:ETEL",  # Telecom Egypt
    "EGX:CIB",   # Commercial International Bank
    "EGX:HERM",  # Hermes
    "EGX:SWDY",  # El Sewedy Electric
    "EGX:ORWE",  # Oriental Weavers
    "EGX:EMFD",  # EFG Hermes
    "EGX:PHDC",  # Palm Hills
    "EGX:TMGH",  # TMG Holding
    "EGX:AMER",  # Amer Group
    "EGX:MNHD",  # Madinet Nasr Housing
]

# Time intervals to process
intervals = ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]

# Real-time monitoring settings
realtime_update_interval_secs = 60
max_historical_candles = 1000
