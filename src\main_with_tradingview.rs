use paragon::{
    connections::database::init_pool,
    handlers::candle::aggregate_candle,
    data_sources::{DataSource, TradingViewSource, tradingview::EgxSymbols},
    TIMERANGES,
    utils::temporary,
};

use futures::future::join_all;
use std::sync::Arc;
use std::env;

#[tokio::main]
async fn main() -> Result<(), String> {
    // Initialize the database connection pool
    init_pool().await?;

    // Determine data source from environment variable
    let data_source = env::var("DATA_SOURCE").unwrap_or_else(|_| "file".to_string());

    match data_source.as_str() {
        "tradingview" => run_with_tradingview().await,
        "file" => run_with_file().await,
        _ => {
            eprintln!("Unknown data source: {}. Use 'tradingview' or 'file'", data_source);
            Err("Invalid data source".to_string())
        }
    }
}

async fn run_with_tradingview() -> Result<(), String> {
    println!("🚀 Starting Paragon with TradingView data source...");

    // Initialize TradingView source with scraper
    let scraper_path = env::var("TV_SCRAPER_PATH")
        .unwrap_or_else(|_| "src/data_sources/tradingview_scraper.py".to_string());

    let tv_source = TradingViewSource::new(scraper_path);

    // Get EGX symbols to monitor
    let symbols = EgxSymbols::get_all_symbols();
    println!("📊 Monitoring {} EGX symbols: {:?}", symbols.len(), symbols);

    // Process each symbol
    for symbol in symbols {
        println!("🔄 Processing symbol: {}", symbol);
        
        // Fetch current price
        match tv_source.fetch_current_price(&symbol).await {
            Ok(price) => println!("💰 Current price for {}: {}", symbol, price),
            Err(e) => eprintln!("❌ Failed to fetch price for {}: {}", symbol, e),
        }

        // Fetch historical candles for each timeframe
        for timerange in TIMERANGES.iter() {
            match tv_source.fetch_historical_candles(&symbol, timerange.label, Some(100)).await {
                Ok(candles) => {
                    println!("📈 Fetched {} candles for {} ({})", candles.len(), symbol, timerange.label);
                    
                    // Process each candle through the aggregation system
                    for candle in candles {
                        let candle_arc = Arc::new(candle);
                        
                        // Aggregate the candle for all timeframes
                        let mut handles = Vec::new();
                        for tr in TIMERANGES.iter() {
                            let cloned_candle = Arc::clone(&candle_arc);
                            let symbol_clone = symbol.clone();
                            let task = tokio::spawn(async move {
                                aggregate_candle(cloned_candle, &symbol_clone, tr).await
                            });
                            handles.push(task);
                        }
                        
                        // Wait for all aggregations to complete
                        let results = join_all(handles).await;
                        
                        // Check for errors
                        for result in results {
                            if let Err(e) = result {
                                eprintln!("❌ Aggregation task failed: {:?}", e);
                            }
                        }
                    }
                },
                Err(e) => eprintln!("❌ Failed to fetch candles for {} ({}): {}", symbol, timerange.label, e),
            }
        }
        
        // Add a small delay between symbols to avoid rate limiting
        tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
    }

    println!("✅ TradingView data processing completed!");
    Ok(())
}

async fn run_with_file() -> Result<(), String> {
    println!("📁 Starting Paragon with file data source...");

    // Load the data from file (existing functionality)
    let data = temporary::get_data().map_err(|e| e.to_string())?;
    println!("📊 Loaded {} rows from file", data.height());

    // Iterate over each row in the data
    for index in 0..data.height() {
        let row = data.get_row(index).map_err(|e| e.to_string())?;
        let parsed_candle = temporary::parse_candle(row).map_err(|e| e.to_string())?;

        let candle = Arc::new(parsed_candle);

        // Spawn a task for each timerange to aggregate the candle
        let mut handles = Vec::new();
        for timerange in TIMERANGES.iter() {
            let cloned_candle = Arc::clone(&candle);
            let task = tokio::spawn(async move {
                aggregate_candle(cloned_candle, "EURUSD", timerange).await
            });

            handles.push(task);
        }

        // Wait for all tasks to complete
        let results = join_all(handles).await;
        
        // Check for errors
        for result in results {
            if let Err(e) = result {
                eprintln!("❌ Aggregation task failed: {:?}", e);
            }
        }
    }

    println!("✅ File data processing completed!");
    Ok(())
}

/// Real-time monitoring mode for TradingView
pub async fn run_realtime_monitoring() -> Result<(), String> {
    println!("🔴 Starting real-time monitoring mode...");
    
    let scraper_path = env::var("TV_SCRAPER_PATH")
        .unwrap_or_else(|_| "src/data_sources/tradingview_scraper.py".to_string());

    let tv_source = TradingViewSource::new(scraper_path);
    let symbols = EgxSymbols::get_all_symbols();
    
    // Monitor every 60 seconds
    let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(60));
    
    loop {
        interval.tick().await;
        
        println!("🔄 Real-time update cycle started...");
        
        for symbol in &symbols {
            match tv_source.fetch_current_price(symbol).await {
                Ok(price) => {
                    println!("💰 {} -> ${:.4}", symbol, price);
                    
                    // Here you could create a candle from the current price
                    // and process it through your aggregation system
                },
                Err(e) => eprintln!("❌ Failed to fetch {}: {}", symbol, e),
            }
        }
        
        println!("✅ Real-time update cycle completed");
    }
}
