use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct Config {
    pub database: DatabaseConfig,
    pub tradingview: TradingViewConfig,
    pub monitoring: MonitoringConfig,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub host: String,
    pub port: u16,
    pub user: String,
    pub password: Option<String>,
    pub dbname: String,
    pub max_connections: u32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TradingViewConfig {
    pub api_base_url: String,
    pub max_concurrent_requests: usize,
    pub rate_limit_delay_ms: u64,
    pub enabled: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    pub symbols: Vec<String>,
    pub intervals: Vec<String>,
    pub realtime_update_interval_secs: u64,
    pub max_historical_candles: usize,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            database: DatabaseConfig::default(),
            tradingview: TradingViewConfig::default(),
            monitoring: MonitoringConfig::default(),
        }
    }
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            host: env::var("DB_HOST").unwrap_or_else(|_| "localhost".to_string()),
            port: env::var("DB_PORT")
                .unwrap_or_else(|_| "5432".to_string())
                .parse()
                .unwrap_or(5432),
            user: env::var("DB_USER").unwrap_or_else(|_| "postgres".to_string()),
            password: env::var("DB_PASSWORD").ok(),
            dbname: env::var("DB_NAME").unwrap_or_else(|_| "Paragon".to_string()),
            max_connections: env::var("DB_MAX_CONNECTIONS")
                .unwrap_or_else(|_| "10".to_string())
                .parse()
                .unwrap_or(10),
        }
    }
}

impl Default for TradingViewConfig {
    fn default() -> Self {
        Self {
            api_base_url: env::var("TV_API_BASE_URL")
                .unwrap_or_else(|_| "http://127.0.0.1:8000".to_string()),
            max_concurrent_requests: env::var("TV_MAX_CONCURRENT")
                .unwrap_or_else(|_| "5".to_string())
                .parse()
                .unwrap_or(5),
            rate_limit_delay_ms: env::var("TV_RATE_LIMIT_MS")
                .unwrap_or_else(|_| "500".to_string())
                .parse()
                .unwrap_or(500),
            enabled: env::var("TV_ENABLED")
                .unwrap_or_else(|_| "true".to_string())
                .parse()
                .unwrap_or(true),
        }
    }
}

impl Default for MonitoringConfig {
    fn default() -> Self {
        Self {
            symbols: vec![
                "EGX:ETEL".to_string(),
                "EGX:CIB".to_string(),
                "EGX:HERM".to_string(),
                "EGX:SWDY".to_string(),
                "EGX:ORWE".to_string(),
            ],
            intervals: vec![
                "1m".to_string(),
                "5m".to_string(),
                "15m".to_string(),
                "1h".to_string(),
                "4h".to_string(),
                "1d".to_string(),
            ],
            realtime_update_interval_secs: env::var("REALTIME_INTERVAL")
                .unwrap_or_else(|_| "60".to_string())
                .parse()
                .unwrap_or(60),
            max_historical_candles: env::var("MAX_HISTORICAL_CANDLES")
                .unwrap_or_else(|_| "1000".to_string())
                .parse()
                .unwrap_or(1000),
        }
    }
}

impl Config {
    /// Load configuration from environment variables
    pub fn from_env() -> Self {
        Self::default()
    }

    /// Load configuration from a TOML file
    pub fn from_file(path: &str) -> Result<Self, Box<dyn std::error::Error>> {
        let content = std::fs::read_to_string(path)?;
        let config: Config = toml::from_str(&content)?;
        Ok(config)
    }

    /// Save configuration to a TOML file
    pub fn save_to_file(&self, path: &str) -> Result<(), Box<dyn std::error::Error>> {
        let content = toml::to_string_pretty(self)?;
        std::fs::write(path, content)?;
        Ok(())
    }

    /// Validate the configuration
    pub fn validate(&self) -> Result<(), String> {
        if self.database.host.is_empty() {
            return Err("Database host cannot be empty".to_string());
        }

        if self.database.user.is_empty() {
            return Err("Database user cannot be empty".to_string());
        }

        if self.database.dbname.is_empty() {
            return Err("Database name cannot be empty".to_string());
        }

        if self.tradingview.enabled && self.tradingview.api_base_url.is_empty() {
            return Err("TradingView API base URL cannot be empty when enabled".to_string());
        }

        if self.monitoring.symbols.is_empty() {
            return Err("At least one symbol must be configured for monitoring".to_string());
        }

        if self.monitoring.intervals.is_empty() {
            return Err("At least one interval must be configured for monitoring".to_string());
        }

        Ok(())
    }
}
