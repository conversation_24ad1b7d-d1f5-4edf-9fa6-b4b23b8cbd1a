# PowerShell script to test TradingView API
Write-Host "🧪 Testing TradingView API Connection" -ForegroundColor Green

$baseUrl = "http://127.0.0.1:8000"

# Test 1: Check if server is running
Write-Host "`n📡 Testing server connection..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri $baseUrl -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✅ Server is running (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ Server is not running: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "`n💡 Please start your TradingView API server first:" -ForegroundColor Yellow
    Write-Host "   python your_api_server.py" -ForegroundColor Cyan
    exit 1
}

# Test 2: Check the scrape_pairs endpoint
Write-Host "`n📊 Testing scrape_pairs endpoint..." -ForegroundColor Yellow

$testPayload = @{
    pairs = @("EGX:ETEL", "EGX:CIB")
    intervals = @(
        @{ value = "1m" },
        @{ value = "5m" }
    )
} | ConvertTo-Json -Depth 3

Write-Host "Request payload:" -ForegroundColor Cyan
Write-Host $testPayload -ForegroundColor White

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/scrape_pairs" -Method Post -Body $testPayload -ContentType "application/json" -TimeoutSec 30
    
    Write-Host "✅ API call successful!" -ForegroundColor Green
    Write-Host "Response keys: $($response.PSObject.Properties.Name -join ', ')" -ForegroundColor Cyan
    
    # Print sample data
    foreach ($symbol in $response.PSObject.Properties.Name) {
        Write-Host "`n📈 Symbol: $symbol" -ForegroundColor Yellow
        $symbolData = $response.$symbol
        if ($symbolData -and $symbolData.Count -gt 0) {
            $firstData = $symbolData[0]
            Write-Host "  Price: $($firstData.price)" -ForegroundColor White
            Write-Host "  Oscillators: $($firstData.oscillators.Count)" -ForegroundColor White
            Write-Host "  Moving Averages: $($firstData.moving_averages.Count)" -ForegroundColor White
            Write-Host "  Pivots: $($firstData.pivots.Count)" -ForegroundColor White
        } else {
            Write-Host "  No data returned" -ForegroundColor Gray
        }
    }
    
    Write-Host "`n🎉 Your TradingView API is working correctly!" -ForegroundColor Green
    Write-Host "✅ Ready for Rust integration" -ForegroundColor Green
    
} catch {
    Write-Host "❌ API call failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Gray
}
